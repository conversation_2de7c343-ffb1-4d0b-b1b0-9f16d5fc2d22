<view class="container rel">
	<navbar isBack="{{isBack}}"></navbar>
	<view class="bgImg">
		<image src="{{imgObject.bg_my}}" class="imgBlock"></image>
	</view>
	<view class="rel w100" style="z-index:1000">
		<view class="userInfo clearfix">
			<button class="photo fl" wx:if="{{isLogin}}" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
				<image class="avatar" src="{{userAvatar ?userAvatar:'../../static/images/default_avatar.png' }}"
					mode="aspectFill"></image>
			</button>
			<button class="photo fl" wx:else bindtap="toLogin">
				<image class="avatar" src="../../static/images/default_avatar.png" mode="aspectFill"></image>
			</button>
			<view class="fl pl35" wx:if='{{userName}}'>
				<view class="c333 b f32 pt20 ">{{userName}}</view>
				<view class="c333 b pt10">{{userPhone?userPhone:''}}</view>
			</view>
		</view>
		<view class="pl30 pr30 w100">
			<!-- 药品订单、问诊订单 -->
			<view class="orderMenu w100">
				<view class="order-item br20" bindtap="allOrder" style="background-image: url('{{imgObject.bg_order_medicine}}'); background-size: contain; background-repeat: no-repeat;">
					<image class="w60 h60 mb28" src="{{imgObject.ic_order_medicine}}" />
					<view class="flex_jss">
						<text class="c333 f28 b600">药品订单</text>
						<image class="w32 h32" src="{{imgObject.ic_order_more}}" />
					</view>
				</view>
				<view class="order-item br20" bindtap="allOrder" style="background-image: url('{{imgObject.bg_order_consultation}}'); background-size: contain; background-repeat: no-repeat;">
					<image class="w60 h60 mb28" src="{{imgObject.ic_order_consultation}}" />
					<view class="flex_jss">
						<text class="c333 f28 b600">问诊订单</text>
						<image class="w32 h32" src="{{imgObject.ic_order_more}}" />
					</view>
				</view>
			</view>
			<!-- 健康服务 -->
			<view class="br20 bg-color-white w100 menu mt20 p28">
				<view class="f32 c333 b600">健康服务</view>
				<view class="flex_jss mt40">
					<!-- 就诊人管理 -->
					<view class="health-item">
						<navigator open-type="navigate" url="/pages/peopleContent/people/people?type=1&source=2" hover-class="none"
							class="center">
							<view class="w48 h48">
								<image src="{{imgObject.ic_my_patient}}" class="imgBlock"></image>
							</view>
							<text class="c666 f24 mt12">就诊人管理</text>
						</navigator>
					</view>
					<!-- 健康档案 -->
					 <view class="health-item">
						<view class="center" bindtap="handleHealthRecords">
							<view class="w48 h48">
								<image src="{{imgObject.ic_my_health_records}}" class="imgBlock"></image>
							</view>
							<text class="c666 f24 mt12">健康档案</text>
						</view>
					</view>
					<!-- 随访计划 -->
					 <view class="health-item">
						<navigator open-type="navigate" url="/pages/follow/calendar/index" hover-class="none" class="center">
							<view class="w48 h48">
								<image src="{{imgObject.ic_my_follow}}" class="imgBlock"></image>
							</view>
							<text class="c666 f24 mt12">随访计划</text>
						</navigator>
					</view>
					<!-- 我的处方 -->
					<view class="health-item">
						<navigator open-type="navigate" url="/pages/recipe/recipe" hover-class="none" class="center">
							<view class="w48 h48">
								<image src="{{imgObject.ic_my_prescription}}" class="imgBlock"></image>
							</view>
							<text class="c666 f24 mt12">我的处方</text>
						</navigator>
					</view>
				</view>
			</view>
			<!-- 其他 -->
			<view class="br20 bg-color-white w100 menu mt20 p40">
				<view class="f32 c333 b600">其他</view>
				<view class="flex_jf mt40">
					<!-- 我的地址 -->
					<view class="health-item" style="margin-right: 96rpx;">
						<navigator open-type="navigate" url="/pages/address/index?type=1" hover-class="none" class="center">
							<view class="w48 h48">
								<image src="{{imgObject.ic_my_address}}" class="imgBlock"></image>
							</view>
							<text class="c666 f24 mt12">我的地址</text>
						</navigator>
					</view>
					<!-- 客服电话 -->
					<view class="center" bindtap="call">
						<view class="w48 h48">
							<image src="{{imgObject.ic_my_service}}" class="imgBlock"></image>
						</view>
						<text class="c666 f24 mt15">客服电话</text>
					</view>
				</view>
			 </view>
		</view>
	</view>
</view>