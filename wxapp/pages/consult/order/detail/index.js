const app = getApp()
const util = require('../../../../utils/util')
const api = require('../../../../config/api.js')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    orderSn: '',
    static: {
      bg_video_interrogation: api.ImgUrl + 'images/bg_video_interrogation_01.png',
      ic_cancel_01: api.ImgUrl + 'images/ic_cancel_01.png',
      ic_completed_01: api.ImgUrl + 'images/ic_completed_01.png',
      ic_in_progress: api.ImgUrl + 'images/ic_in_progress.png',
      ic_refund: api.ImgUrl + 'images/ic_refund.png',
      ic_waiting_02: api.ImgUrl + 'images/ic_waiting_02.png'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      orderSn: options.orderSn
    }, () => {
      this.getData()
    })
  },
  async getData() {
    const { orderSn } = this.data
    try {
      const { data } = await util.request(`${api.consultOrderDetail}?orderSn=${orderSn}`)
      if (data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
        return
      }
      console.log(data, 'data')

      // 处理图片资料数据
      if (data.data.offlineDiagnosisImg && data.data.offlineDiagnosisImg.trim() !== '') {
        // 如果有图片数据，按逗号分割并过滤空字符串
        data.data.offlineDiagnosisImg = data.data.offlineDiagnosisImg.split(',').filter(img => img.trim() !== '')
      } else {
        // 如果没有图片数据，设置为空数组
        data.data.offlineDiagnosisImg = []
      }

      console.log('处理后的图片资料:', data.data.offlineDiagnosisImg)

      this.setData({
        detail: data.data
      })
      console.log(this.data.detail)
    } catch (error) {
      throw new Error(error)
    }
  },
  handleConsult(e) {
    const { doctorid, status, type, id, roomid } = e.currentTarget.dataset

    console.log('按钮点击参数:', { doctorid, status, type, id, roomid })

    // 视频咨询逻辑
    if (type === 2) {
      if (status === 1) {
        // 待接诊状态：呼叫视频医生 - 直接跳转到呼叫医生页面
        this.callVideoDoctor(id)
      } else if (status === 2) {
        // 进行中状态：进入诊室 - 跳转到视频通话页面
        wx.navigateTo({
          url: `/pages/meeting/meeting?roomID=${roomid}&videoConsultId=${id}`
        })
      } else if (status === 3 || status === 4) {
        // 已完成或已取消状态：再次咨询 - 调用后端接口获取招商H5地址
        this.getConsultAgainUrl()
      } else {
        // 其他状态：默认跳转到视频咨询室
        wx.navigateTo({
          url: `/pages_videoConsult/consultRoom/index?doctorId=${doctorid}&consultId=${id}`
        })
      }
    }
    // 图文咨询逻辑（保持原有逻辑）
    else if (type === 1) {
      if (status === 3 || status === 4 || status === 5) {
        // 已完成、已取消、已退款状态：再次咨询
        wx.navigateTo({
          url: `/pages/famousDoctorDetail/famousDoctorDetail?type=3&doctorId=${doctorid}`
        })
      } else {
        // 其他状态：继续咨询
        wx.navigateTo({
          url: `/pages/consult/chat/chat?doctorId=${doctorid}`
        })
      }
    }
    // 兜底逻辑：未知类型按原有逻辑处理
    else {
      if (status === 3 || status === 4 || status === 5) {
        wx.navigateTo({
          url: `/pages/famousDoctorDetail/famousDoctorDetail?type=3&doctorId=${doctorid}`
        })
      } else {
        wx.navigateTo({
          url: `/pages/consult/chat/chat?doctorId=${doctorid}`
        })
      }
    }
  },

  /**
     * 呼叫医生（视频问诊-待接诊状态）
     */
  async callVideoDoctor(id) {
    try {
      // 显示loading状态
      wx.showLoading({
        title: '发起视频通话...',
        mask: true
      })

      // 构建接口参数
      const params = {
        patientId: app.globalData.userInfo.userId, // 患者ID
        videoConsultId: id // 视频咨询ID
      }

      console.log('调用视频拨号接口，参数:', params)

      // 验证必要参数
      if (!params.patientId) {
        wx.hideLoading()
        util.showToast({
          title: '用户信息异常，请重新登录',
          icon: 'none'
        })
        return
      }

      if (!params.videoConsultId) {
        wx.hideLoading()
        util.showToast({
          title: '问诊信息异常，请重试',
          icon: 'none'
        })
        return
      }

      // 调用视频拨号接口
      const res = await util.request(api.startVideoCall, params, 'post', '2')

      wx.hideLoading()

      if (res.data.code === 0) {
        console.log('视频拨号接口调用成功:', res.data.data)

        // 从接口响应中提取医生信息
        const responseData = res.data.data
        console.log('startVideoCall API 完整响应数据结构:', JSON.stringify(responseData, null, 2))

        // 提取医生信息，支持多种可能的字段名
        const doctorInfo = this.extractDoctorInfo(responseData)
        console.log('提取的医生信息:', doctorInfo)

        // 将医生信息存储到全局数据中，供呼叫医生页面使用
        if (doctorInfo && Object.keys(doctorInfo).length > 0) {
          if (app.globalData) {
            app.globalData.videoCallDoctorInfo = doctorInfo
            console.log('存储医生信息到全局数据:', doctorInfo)
          }
        }

        // 构建跳转URL，包含基础参数
        let finalUrl = `/pages_videoConsult/callDoctor/index?videoConsultId=${params.videoConsultId}&patientId=${params.patientId}`

        // 添加医生信息参数到URL（作为备用方案）
        if (doctorInfo) {
          finalUrl = this.addDoctorInfoToUrl(finalUrl, doctorInfo)
        }

        console.log('跳转到呼叫医生页面:', finalUrl)

        wx.navigateTo({
          url: finalUrl
        })
      } else {
        console.error('视频拨号接口调用失败:', res.data.msg)
        util.showToast({
          title: res.data.msg || '发起视频通话失败',
          icon: 'none',
          duration: 3000
        })
      }

    } catch (err) {
      console.error('视频拨号接口调用异常:', err)
      wx.hideLoading()
      util.showToast({
        title: '网络异常，请稍后重试',
        icon: 'none',
        duration: 3000
      })
    }
  },

  /**
   * 从API响应中提取医生信息
   * @param {Object} responseData API响应数据
   * @returns {Object} 提取的医生信息对象
   */
  extractDoctorInfo(responseData) {
    try {
      // 如果响应数据为空，返回空对象
      if (!responseData || typeof responseData !== 'object') {
        console.log('响应数据为空或格式不正确')
        return {}
      }

      // 提取医生信息，支持多种可能的字段名
      const doctorInfo = {
        // 医生头像 - 支持多种字段名
        avatar: responseData.doctorHeadUrl ||
                responseData.doctorPhoto ||
                responseData.headUrl ||
                responseData.avatar ||
                responseData.photo || '',

        // 医生姓名 - 支持多种字段名
        name: responseData.doctorName ||
              responseData.name ||
              responseData.realName || '医生',

        // 医生职称 - 支持多种字段名
        title: responseData.doctorTitle ||
               responseData.title ||
               responseData.position ||
               responseData.jobTitle || '',

        // 医生专长 - 支持多种字段名
        specialty: responseData.doctorExpertise ||
                   responseData.expertise ||
                   responseData.specialty ||
                   responseData.goodAt ||
                   responseData.speciality || '',

        // 其他可能有用的医生信息
        goodRate: responseData.goodRate || '',
        serviceCount: responseData.serviceCount || 0,
        department: responseData.department || responseData.deptName || '',
        hospital: responseData.hospital || responseData.hospitalName || ''
      }

      // 过滤掉空值，只保留有效的医生信息
      const filteredDoctorInfo = {}
      Object.keys(doctorInfo).forEach(key => {
        if (doctorInfo[key] !== '' && doctorInfo[key] !== null && doctorInfo[key] !== undefined) {
          filteredDoctorInfo[key] = doctorInfo[key]
        }
      })

      console.log('过滤后的医生信息:', filteredDoctorInfo)
      return filteredDoctorInfo

    } catch (error) {
      console.error('提取医生信息时发生错误:', error)
      return {}
    }
  },

  /**
   * 将医生信息添加到跳转URL中（作为备用传递方案）
   * @param {String} baseUrl 基础URL
   * @param {Object} doctorInfo 医生信息对象
   * @returns {String} 包含医生信息参数的完整URL
   */
  addDoctorInfoToUrl(baseUrl, doctorInfo) {
    try {
      let url = baseUrl

      // 只添加关键的医生信息到URL参数中，避免URL过长
      if (doctorInfo.name && doctorInfo.name !== '医生') {
        url += `&doctorName=${encodeURIComponent(doctorInfo.name)}`
      }

      if (doctorInfo.title) {
        url += `&doctorTitle=${encodeURIComponent(doctorInfo.title)}`
      }

      if (doctorInfo.avatar) {
        url += `&doctorAvatar=${encodeURIComponent(doctorInfo.avatar)}`
      }

      if (doctorInfo.specialty) {
        // 限制专长描述长度，避免URL过长
        const shortSpecialty = doctorInfo.specialty.length > 50
          ? doctorInfo.specialty.substring(0, 50) + '...'
          : doctorInfo.specialty
        url += `&doctorSpecialty=${encodeURIComponent(shortSpecialty)}`
      }

      return url

    } catch (error) {
      console.error('添加医生信息到URL时发生错误:', error)
      return baseUrl // 如果出错，返回原始URL
    }
  },

  /**
   * 获取再次咨询的H5地址
   */
  async getConsultAgainUrl() {
    try {
      util.showToast({
        title: '加载中...',
        icon: 'loading'
      })

      // TODO: 替换为实际的API接口地址
      const params = {
        patientId: app.globalData.userInfo.userId
      }

      console.log('获取再次咨询H5地址参数:', params)

      // 调用后端接口获取招商H5地址
      const { data } = await util.request(api.getVideoConsultUrl, params, 'get')

      util.hideToast()

      if (data.code === 0) {
        const h5Url = data.data
        if (h5Url) {
          console.log('跳转到招商H5地址:', h5Url)
          wx.navigateTo({
            url: `/pages/webView/index?url=${encodeURIComponent(h5Url)}`
          })
        } else {
          util.showToast({
            title: '获取咨询地址失败',
            icon: 'none'
          })
        }
      } else {
        util.showToast({
          title: data.msg || '获取咨询地址失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('获取再次咨询H5地址失败:', error)
      util.hideToast()
      util.showToast({
        title: '网络异常，请稍后重试',
        icon: 'none'
      })
    }
  },

  handlePreview: function(e) {
    const { detail } = this.data
    var { index } = e.currentTarget.dataset
    wx.previewImage({
      current: detail.offlineDiagnosisImg[index],
      urls: detail.offlineDiagnosisImg
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})
