const api = require('../../config/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '服务中心',
    bg_yaoying: api.ImgUrl + 'images/video_consult/<EMAIL>',
    // 从H5页面接收的参数
    targetAppId: '',
    targetPath: '',
    extraData: {}
  },

  onLoad(options) {
    console.log('耀影服务介绍页面加载', options)

    // 解析从H5页面传递过来的参数
    this.parseH5Parameters(options)
  },

  onShow() {
    // 页面显示时的逻辑
  },

  /**
   * 解析从H5页面传递的参数
   * @param {Object} options 页面参数
   */
  parseH5Parameters(options) {
    try {
      // 获取目标小程序的appId
      if (options.appId) {
        this.setData({
          targetAppId: options.appId
        })
        console.log('接收到目标小程序appId:', options.appId)
      }

      // 获取目标页面路径
      if (options.path) {
        // 对路径进行解码处理
        const decodedPath = decodeURIComponent(options.path)
        this.setData({
          targetPath: decodedPath
        })
        console.log('接收到目标页面路径:', decodedPath)
      }

      // 获取其他额外参数
      const extraData = {}
      Object.keys(options).forEach(key => {
        if (key !== 'appId' && key !== 'path') {
          extraData[key] = options[key]
        }
      })

      if (Object.keys(extraData).length > 0) {
        this.setData({
          extraData: extraData
        })
        console.log('接收到额外参数:', extraData)
      }

    } catch (error) {
      console.error('解析H5参数失败:', error)
      util.showToast({
        title: '参数解析失败',
        icon: 'none'
      })
    }
  },

  /**
   * 参数验证
   * @returns {Boolean} 验证结果
   */
  validateParameters() {
    if (!this.data.targetAppId) {
      util.showToast({
        title: '缺少目标小程序ID',
        icon: 'none'
      })
      return false
    }

    if (!this.data.targetPath) {
      util.showToast({
        title: '缺少目标页面路径',
        icon: 'none'
      })
      return false
    }

    return true
  },

  /**
   * 立即问诊按钮点击事件 - 跳转到目标小程序
   */
  handleConsult() {
    console.log('点击立即问诊按钮')

    // 验证参数
    if (!this.validateParameters()) {
      console.log('参数验证失败')
      return
    }

    // 显示加载提示
    util.showToast({
      title: '正在跳转...',
      icon: 'loading'
    })

    // 跳转到目标小程序
    wx.navigateToMiniProgram({
      appId: this.data.targetAppId,
      path: this.data.targetPath,
      extraData: this.data.extraData,
      envVersion: 'release', // 正式版
      success: (res) => {
        console.log('跳转到耀影小程序成功:', res)
        util.hideToast()
      },
      fail: (err) => {
        console.error('跳转到耀影小程序失败:', err)
        util.hideToast()

        // 根据错误类型显示不同的提示信息
        let errorMsg = '跳转失败，请稍后重试'
        if (err.errMsg) {
          if (err.errMsg.includes('cancel')) {
            errorMsg = '用户取消跳转'
          } else if (err.errMsg.includes('appId')) {
            errorMsg = '目标小程序不存在或未发布'
          } else if (err.errMsg.includes('path')) {
            errorMsg = '目标页面路径错误'
          }
        }

        util.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        })
      }
    })
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
  }
})
