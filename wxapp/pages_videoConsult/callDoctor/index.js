const app = getApp()
const api = require('../../config/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    btn_hang_up_01: api.ImgUrl + 'images/video_consult/<EMAIL>',
    doctorInfo: {},
    callStatus: 'calling', // calling: 呼叫中, ended: 已结束
    statusBarHeight: app.globalData.statusBarHeight,

    // 页面参数
    doctorId: null,
    consultId: null,
    inquirerId: null,
    videoConsultId: null,
    subOrderCode: null,
    packageCode: null
  },

  // 音频相关
  phoneRingAudio: null, // 呼叫铃声音频对象
  playEndAudio: null, // 结束音效音频对象

  onLoad(options) {
    console.log('呼叫医生页面加载', options)

    // 保存页面参数（包括医生信息参数）
    this.setData({
      doctorId: options.doctorId,
      consultId: options.consultId,
      inquirerId: options.inquirerId,
      videoConsultId: options.videoConsultId,
      subOrderCode: options.subOrderCode,
      packageCode: options.packageCode,
      // 保存从URL传递的医生信息参数
      doctorName: options.doctorName,
      doctorTitle: options.doctorTitle,
      doctorAvatar: options.doctorAvatar,
      doctorSpecialty: options.doctorSpecialty
    })

    // 初始化音频
    this.initAudio()

    // 获取医生信息 - 优先使用全局数据，其次使用URL参数，最后调用API
    this.loadDoctorInfo()

    // 模拟呼叫状态
    this.startCalling()
  },

  onUnload() {
    // 页面卸载时的清理工作
    this.stopAllAudio()
  },

  /**
   * 初始化音频
   */
  initAudio() {
    try {
      // 初始化呼叫铃声
      this.phoneRingAudio = wx.createInnerAudioContext()
      this.phoneRingAudio.src = '/static/audio/phonering.mp3'
      this.phoneRingAudio.loop = true // 设置循环播放
      this.phoneRingAudio.volume = 0.8 // 设置音量

      // 初始化结束音效
      this.playEndAudio = wx.createInnerAudioContext()
      this.playEndAudio.src = '/static/audio/playend.mp3'
      this.playEndAudio.loop = false
      this.playEndAudio.volume = 0.8

      // 监听音频事件
      this.phoneRingAudio.onError((err) => {
        console.error('呼叫铃声播放错误:', err)
        // 错误不影响功能，继续执行
      })

      this.playEndAudio.onError((err) => {
        console.error('结束音效播放错误:', err)
        // 错误不影响功能，继续执行
      })

      this.playEndAudio.onEnded(() => {
        console.log('结束音效播放完毕')
      })

      // 监听呼叫铃声加载完成
      this.phoneRingAudio.onCanplay(() => {
        console.log('呼叫铃声加载完成')
      })

      this.playEndAudio.onCanplay(() => {
        console.log('结束音效加载完成')
      })

    } catch (err) {
      console.error('音频初始化失败:', err)
      // 即使音频初始化失败，页面功能仍然可用
    }
  },

  /**
   * 停止所有音频
   */
  stopAllAudio() {
    try {
      if (this.phoneRingAudio) {
        this.phoneRingAudio.stop()
        this.phoneRingAudio.destroy()
        this.phoneRingAudio = null
      }
      if (this.playEndAudio) {
        this.playEndAudio.stop()
        this.playEndAudio.destroy()
        this.playEndAudio = null
      }
    } catch (err) {
      console.error('停止音频失败:', err)
    }
  },

  /**
   * MQTT消息到达处理
   */
  onMessageArrived() {
    const message = arguments[0]
    console.log('呼叫医生页面收到MQTT消息:', message)

    // 验证消息格式
    if (!message || typeof message !== 'object') {
      console.warn('无效的消息格式:', message)
      return
    }

    try {
      // 处理视频相关消息
      this.handleVideoMessage(message)
    } catch (err) {
      console.error('处理MQTT消息异常:', err)
    }
  },

  /**
   * 处理视频相关消息
   */
  handleVideoMessage(message) {
    // 只处理视频相关消息 (c=1)
    if (message.c !== 1) {
      return
    }

    let messageData = null
    let videoConsultId = null

    // 解析消息体中的i字段（JSON字符串）
    try {
      if (typeof message.i === 'string') {
        messageData = JSON.parse(message.i)
        videoConsultId = messageData.videoConsultId
      } else if (typeof message.i === 'object') {
        messageData = message.i
        videoConsultId = messageData.videoConsultId
      }
    } catch (parseErr) {
      console.error('解析消息体失败:', parseErr, message.i)
      return
    }

    // 验证videoConsultId是否匹配当前页面
    if (!videoConsultId || !this.isCurrentVideoConsult(videoConsultId)) {
      console.log('videoConsultId不匹配，忽略消息:', {
        messageVideoConsultId: videoConsultId,
        currentVideoConsultId: this.data.videoConsultId,
        currentConsultId: this.data.consultId
      })
      return
    }

    console.log('处理视频消息:', {
      type: message.t,
      videoConsultId: videoConsultId,
      messageData: messageData
    })

    // 根据消息类型处理
    switch (message.t) {
      case 15:
        // 视频接诊消息 - 医生已接诊
        this.handleVideoAccepted(messageData)
        break
      case 16:
        // 视频挂断消息 - 通话挂断
        this.handleVideoHangUp(messageData)
        break
      default:
        console.log('未处理的视频消息类型:', message.t)
        break
    }
  },

  /**
   * 验证是否为当前视频咨询
   */
  isCurrentVideoConsult(messageVideoConsultId) {
    // 转换为字符串进行比较，避免类型不匹配
    const currentVideoConsultId = String(this.data.videoConsultId || '')
    const currentConsultId = String(this.data.consultId || '')
    const msgVideoConsultId = String(messageVideoConsultId || '')

    return msgVideoConsultId === currentVideoConsultId ||
           msgVideoConsultId === currentConsultId ||
           (currentVideoConsultId && msgVideoConsultId === currentVideoConsultId)
  },

  /**
   * 处理视频接诊消息 (t=15)
   */
  handleVideoAccepted(messageData) {
    console.log('收到视频接诊消息，医生已接诊:', messageData)

    // 停止呼叫铃声
    this.stopCallingRing()

    // 更新UI状态
    this.setData({
      callStatus: 'accepted'
    })

    // 显示提示
    wx.showToast({
      title: '医生已接诊',
      icon: 'success',
      duration: 1500
    })

    // 调用getAppIdAndKey方法进入视频通话
    setTimeout(() => {
      this.getAppIdAndKey()
    }, 1500)
  },

  /**
   * 处理视频挂断消息 (t=16)
   */
  handleVideoHangUp(messageData) {
    console.log('收到视频挂断消息:', messageData)

    // 停止呼叫铃声
    this.stopCallingRing()

    // 更新UI状态
    this.setData({
      callStatus: 'ended'
    })

    // 播放结束音效
    if (this.playEndAudio) {
      this.playEndAudio.play()
    }

    // 显示提示
    wx.showToast({
      title: '通话已结束',
      icon: 'none',
      duration: 2000
    })

    // 延迟返回上一页
    setTimeout(() => {
      wx.navigateBack({ delta: 1 })
    }, 2000)
  },

  /**
   * 停止呼叫铃声
   */
  stopCallingRing() {
    if (this.phoneRingAudio) {
      try {
        this.phoneRingAudio.stop()
      } catch (err) {
        console.error('停止呼叫铃声失败:', err)
      }
    }
  },

  /**
   * 获取appId和key
   */
  getAppIdAndKey() {
    const { videoConsultId, roomId } = this.data
    util.request(api.getAppIdAndKey, {
      videoConsultId: videoConsultId
    })
      .then(res => {
        util.hideToast()
        if (res.data.code === 0) {
          wx.navigateTo({
            url: `/pages/meeting/meeting?roomID=${roomId}&videoConsultId=${videoConsultId}`
          })
        } else {
          util.showToast({
            icon: 'none',
            title: res.data.msg
          })
        }
      })
      .catch(err => {
        console.log(err)
      })
  },

  /**
   * 加载医生信息 - 优先使用全局数据，其次使用URL参数，最后调用API获取
   */
  loadDoctorInfo() {
    const app = getApp()

    // 第一优先级：使用从视频咨询室传递过来的全局数据中的医生信息
    if (app.globalData && app.globalData.videoCallDoctorInfo) {
      const doctorInfo = app.globalData.videoCallDoctorInfo
      console.log('使用全局数据中的医生信息:', doctorInfo)

      this.setData({
        doctorInfo: {
          name: doctorInfo.name || '医生',
          title: doctorInfo.title || '',
          photo: doctorInfo.avatar || doctorInfo.photo || '/static/images/doctor_icon.png',
          goodRate: doctorInfo.goodRate || '100%',
          serviceCount: doctorInfo.serviceCount || 0,
          specialty: doctorInfo.specialty || doctorInfo.expertise
        }
      })

      // 清除全局数据，避免影响其他页面
      delete app.globalData.videoCallDoctorInfo
    } else {
      // 第二优先级：尝试从URL参数中获取医生信息
      const urlDoctorInfo = this.extractDoctorInfoFromUrl()
      if (urlDoctorInfo && Object.keys(urlDoctorInfo).length > 0) {
        console.log('使用URL参数中的医生信息:', urlDoctorInfo)
        this.setData({
          doctorInfo: urlDoctorInfo
        })
      } else if (this.data.doctorId) {
        // 第三优先级：如果没有全局数据和URL参数，则调用API获取医生信息
        this.getDoctorInfo(this.data.doctorId)
      } else {
        // 最后：如果都没有，使用默认医生信息
        this.setDefaultDoctorInfo()
      }
    }
  },

  /**
   * 从URL参数中提取医生信息
   * @returns {Object} 医生信息对象
   */
  extractDoctorInfoFromUrl() {
    try {
      const doctorInfo = {}

      // 从URL参数中提取医生信息
      if (this.data.doctorName) {
        doctorInfo.name = decodeURIComponent(this.data.doctorName)
      }

      if (this.data.doctorTitle) {
        doctorInfo.title = decodeURIComponent(this.data.doctorTitle)
      }

      if (this.data.doctorAvatar) {
        doctorInfo.photo = decodeURIComponent(this.data.doctorAvatar)
      }

      if (this.data.doctorSpecialty) {
        doctorInfo.specialty = decodeURIComponent(this.data.doctorSpecialty)
      }

      // 设置默认值
      const finalDoctorInfo = {
        name: doctorInfo.name || '医生',
        title: doctorInfo.title || '',
        photo: doctorInfo.photo || '/static/images/doctor_icon.png',
        goodRate: '100%', // URL参数中通常不包含这些信息，使用默认值
        serviceCount: 0,
        specialty: doctorInfo.specialty
      }

      // 只有当至少有一个有效的医生信息时才返回
      if (doctorInfo.name || doctorInfo.title || doctorInfo.photo || doctorInfo.specialty) {
        console.log('从URL参数提取的医生信息:', finalDoctorInfo)
        return finalDoctorInfo
      }

      return null

    } catch (error) {
      console.error('从URL参数提取医生信息时发生错误:', error)
      return null
    }
  },

  /**
   * 获取医生信息 - 通过API调用
   */
  async getDoctorInfo(doctorId) {
    try {
      console.log('通过API获取医生信息, doctorId:', doctorId)

      const res = await util.request(api.doctorDetail, {
        doctorId: doctorId,
        patientId: app.globalData.userInfo.userId
      }, 'POST', '2')

      if (res.data.code === 0) {
        const apiDoctorInfo = res.data.data
        console.log('API返回的医生信息:', apiDoctorInfo)

        this.setData({
          doctorInfo: {
            name: apiDoctorInfo.name || '医生',
            title: apiDoctorInfo.title || apiDoctorInfo.doctorTitle || '',
            photo: apiDoctorInfo.photo || apiDoctorInfo.headUrl || '/static/images/doctor_icon.png',
            goodRate: apiDoctorInfo.goodRate || '100%',
            serviceCount: apiDoctorInfo.serviceCount || 0,
            specialty: apiDoctorInfo.specialty || apiDoctorInfo.expertise
          }
        })
      } else {
        console.error('获取医生信息失败', res.data.msg)
        // 使用默认医生信息
        this.setDefaultDoctorInfo()
      }
    } catch (err) {
      console.error('获取医生信息异常', err)
      // 使用默认医生信息
      this.setDefaultDoctorInfo()
    }
  },

  /**
   * 设置默认医生信息
   */
  setDefaultDoctorInfo() {
    this.setData({
      doctorInfo: {
        name: '医生',
        title: '专业医师',
        photo: '/static/images/doctor_icon.png',
        goodRate: '100%',
        serviceCount: 0,
        specialty: ''
      }
    })
  },

  /**
   * 开始呼叫
   */
  startCalling() {
    this.setData({
      callStatus: 'calling'
    })

    // 开始播放呼叫铃声
    if (this.phoneRingAudio) {
      // 延迟一下再播放，确保音频加载完成
      setTimeout(() => {
        this.phoneRingAudio.play()
      }, 100)
    }
  },

  /**
   * 挂断电话
   */
  async hangUp() {
    console.log('用户点击挂断')

    // 更新UI状态
    this.setData({
      callStatus: 'ended'
    })

    // 停止呼叫铃声
    if (this.phoneRingAudio) {
      try {
        this.phoneRingAudio.stop()
      } catch (err) {
        console.error('停止呼叫铃声失败:', err)
      }
    }

    // 播放结束音效
    if (this.playEndAudio) {
      this.playEndAudio.play()
    }

    // 调用挂断接口
    await this.callHangUpAPI()

    // 返回上一页
    wx.navigateBack({ delta: 1 })
  },

  /**
   * 调用挂断视频通话接口
   */
  async callHangUpAPI() {
    try {
      // 构建挂断接口参数
      const params = {
        patientId: app.globalData.userInfo.userId, // 患者ID
        videoConsultId: this.data.videoConsultId // 视频咨询ID
      }

      console.log('调用挂断接口，参数:', params)

      // 验证必要参数
      if (!params.patientId) {
        console.error('挂断接口调用失败: 缺少患者ID')
        return
      }

      if (!params.videoConsultId) {
        console.error('挂断接口调用失败: 缺少视频咨询ID')
        return
      }

      // 调用挂断接口
      const res = await util.request(api.hangUpVideoCall, params, 'POST', '2')

      if (res.data.code === 0) {
        console.log('挂断接口调用成功:', res.data.data)
      } else {
        console.error('挂断接口调用失败:', res.data.msg)
        // 即使接口调用失败，也不阻止用户返回上一页
        wx.showToast({
          title: res.data.msg || '挂断失败，但已结束通话',
          icon: 'none',
          duration: 2000
        })
      }

    } catch (err) {
      console.error('挂断接口调用异常:', err)
      // 网络异常也不阻止用户返回上一页
      wx.showToast({
        title: '网络异常，但已结束通话',
        icon: 'none',
        duration: 2000
      })
    }
  }
})
