<view class="call-container">
  <!-- 状态栏占位 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
  
  <!-- 医生信息区域 -->
  <view class="doctor-info">
    <view class="doctor-avatar">
      <image src="{{doctorInfo.photo || '/static/images/doctor_icon.png'}}" mode="aspectFill"></image>
    </view>

    <view class="doctor-details">
      <view class="doctor-name">{{doctorInfo.name || '医生'}}{{doctorInfo.title ? ' | ' + doctorInfo.title : ''}}</view>
    </view>
  </view>

  <!-- 医生专长介绍 -->
  <view class="doctor-specialty">
    <text>{{doctorInfo.specialty }}</text>
  </view>
  
  <!-- 呼叫状态区域 -->
  <view class="call-status">
    <view wx:if="{{callStatus === 'calling'}}" class="status-calling">
      <text>请耐心等待医生接听...</text>
    </view>
    <view wx:elif="{{callStatus === 'ended'}}" class="status-ended">
      <text>通话已结束</text>
    </view>
  </view>
  
  <!-- 控制按钮区域 -->
  <view class="call-controls">
    <view class="hang-up-btn" bindtap="hangUp">
      <image src="{{btn_hang_up_01}}" mode="aspectFit"></image>
    </view>
    <view class="hang-up-text">挂断</view>
  </view>
</view> 