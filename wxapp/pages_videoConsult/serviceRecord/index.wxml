<navbar isBack="{{isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>

<view class="service-record-container">
  <!-- 主tab栏 -->
  <view class="main-tabs" style="top:{{ 44 + statusBarHeight }}px;">
    <view class="main-tab-item {{mainTabIndex === index ? 'active' : ''}}" 
          wx:for="{{mainTabList}}" 
          wx:key="index"
          data-index="{{index}}"
          bindtap="onMainTabChange">
      <text class="main-tab-text">{{item}}</text>
      <view class="main-tab-line" wx:if="{{mainTabIndex === index}}"></view>
    </view>
    <!-- 问诊子tab栏 -->
    <view class="consult-tabs" style="top: {{44 + statusBarHeight + 40}}px;" wx:if="{{mainTabIndex === 0}}">
      <view class="consult-tab-item {{consultTabIndex === index ? 'active' : ''}}"
            wx:for="{{consultTabList}}"
            wx:key="index"
            data-index="{{index}}"
            data-id="{{ item.id }}"
            bindtap="onConsultTabChange">
        <text class="consult-tab-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 问诊tab内容 -->
  <view class="tab-content" wx:if="{{mainTabIndex === 0}}">

    <!-- 问诊记录列表 -->
    <scroll-view class="consult-scroll-container" 
                 scroll-y="{{true}}" 
                 enable-back-to-top="{{true}}"
                 bindscrolltolower="loadMoreConsult"
                 refresher-enabled="{{true}}"
                 refresher-triggered="{{refreshing}}"
                 bindrefresherrefresh="refreshConsultData">
      <view class="order_list pb10" wx:if="{{consultList.length}}">
        <view class="bg-color-white m20 container-radius"
              wx:for="{{consultList}}"
              wx:key="videoConsultId"
              data-orderSn="{{item.orderSn}}"
              bindtap="onConsultItemClick">
          
          <!-- 顶部状态栏 -->
          <view class="flex_lr_m p20 bb1">
            <view class="flex_m">
              <view class="flex_c_m">
                <image class="w40 h40" src="{{item.consultType === 2 ? static.ic_doctor_video : static.ic_doctor_image}}" mode="aspectFit" />
              </view>
              <text class="f32 c333 b ml16">{{item.consultType === 1 ? '图文问诊' : '视频问诊'}}</text>
            </view>
            <view class="f28">
              <view style="color: #367dff" wx:if="{{item.consultStatus === 1}}">待接诊</view>
              <view style="color: #ff9b3a" wx:if="{{item.consultStatus === 2}}">进行中</view>
              <view style="color: #38bf87" wx:if="{{item.consultStatus === 3}}">已完成</view>
              <view style="color: #666666" wx:if="{{item.consultStatus === 4}}">已取消</view>
            </view>
          </view>
          
          <!-- 详细信息 -->
          <view class="p20">
            <view class="f28 c333">订单号：{{item.orderSn || '-'}}</view>
            <view class="f28 c333 mt10">就诊人：{{item.inquirerName || '-'}} {{item.inquirerGender == 1 ?'男':'女'}} {{ item.inquirerAge }}</view>
            <view class="f28 c333 mt10">问诊医生：{{item.doctorName || '-'}} {{item.departmentName}}</view>
            <view class="f28 c333 mt10">问诊时间：{{item.createdAt || '-'}}</view>
            <view class="f28 c333 mt10">问诊费用：<text class="color-danger-pay b">¥{{item.price / 100}}</text></view>
          </view>
          
          <!-- 底部按钮 -->
          <!-- 视频问诊 - 待接诊 -->
          <view class="pl20 pr20 pb20 flex_c_end" wx:if="{{item.consultType === 2 && item.consultStatus === 1}}">
            <view class="f26 color-primary-btn pt10 pb10 pl30 pr30 container-radius"
                  style="border: 2rpx solid #367DFF"
                  data-action="call_doctor"
                  data-item="{{item}}"
                  catchtap="onActionClick">
              呼叫医生
            </view>
          </view>

          <!-- 视频问诊 - 进行中 -->
          <view class="pl20 pr20 pb20 flex_c_end" wx:elif="{{item.consultType === 2 && item.consultStatus === 2}}">
            <view class="f26 color-primary-btn pt10 pb10 pl30 pr30 container-radius"
                  style="border: 2rpx solid #367DFF"
                  data-action="enter_room"
                  data-item="{{item}}"
                  catchtap="onActionClick">
              进入诊室
            </view>
          </view>

          <!-- 图文咨询 - 待接诊/进行中 -->
          <view class="pl20 pr20 pb20 flex_c_end" wx:elif="{{item.consultType === 1 && (item.consultStatus === 1 || item.consultStatus === 2)}}">
            <view class="f26 color-primary-btn pt10 pb10 pl30 pr30 container-radius"
                  style="border: 2rpx solid #367DFF"
                  data-action="continue_consult"
                  data-item="{{item}}"
                  catchtap="onActionClick">
              继续咨询
            </view>
          </view>

          <!-- 已完成状态 - 视频问诊/图文咨询通用 -->
          <view class="pl20 pr20 pb20 flex_c_end" wx:elif="{{(item.consultType === 1 || item.consultType === 2) && item.consultStatus === 3}}">
            <view class="f26 c666 pt10 pb10 pl20 pr20 container-radius mr15"
                  style="border: 2rpx solid #E4E4E4"
                  data-action="report"
                  data-item="{{item}}"
                  catchtap="onActionClick">
              问诊报告
            </view>
            <view class="f26 c666 pt10 pb10 pl20 pr20 container-radius mr15"
                  style="border: 2rpx solid #E4E4E4"
                  data-action="prescription"
                  data-item="{{item}}"
                  catchtap="onActionClick">
              查看处方
            </view>
            <view class="f26 color-primary-btn pt10 pb10 pl20 pr20 container-radius"
                  style="border: 2rpx solid #367DFF"
                  data-action="evaluate"
                  data-item="{{item}}"
                  catchtap="onActionClick">
              评价医生
            </view>
          </view>

          <!-- 已取消状态 - 视频问诊/图文咨询通用 -->
          <view class="pl20 pr20 pb20 flex_c_end" wx:elif="{{(item.consultType === 1 || item.consultType === 2) && item.consultStatus === 4}}">
            <view class="f26 color-primary-btn pt10 pb10 pl30 pr30 container-radius"
                  style="border: 2rpx solid #367DFF"
                  data-action="consult_again"
                  data-item="{{item}}"
                  catchtap="onActionClick">
              再次咨询
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view wx:else class="flex_line_c no_msg_box">
        <image class="no_msg" src="{{static.nomes}}"></image>
        <view class="f28 c666">暂无{{consultTabList[consultTabIndex]}}记录</view>
      </view>
      
      <!-- 加载更多提示 -->
      <view class="load-more-tip" wx:if="{{consultList.length > 0 && loading}}">
        <text class="f24 c999">加载中...</text>
      </view>
      <view class="load-more-tip" wx:if="{{consultList.length > 0 && !hasMoreConsult && !loading}}">
        <text class="f24 c999">没有更多数据了</text>
      </view>
    </scroll-view>
  </view>

  <!-- 处方tab内容 -->
  <view class="tab-content" wx:if="{{mainTabIndex === 1}}">
    <scroll-view class="prescription-scroll-container"
                 scroll-y="{{true}}"
                 enable-back-to-top="{{true}}"
                 bindscrolltolower="loadMorePrescription"
                 refresher-enabled="{{true}}"
                 refresher-triggered="{{refreshing}}"
                 bindrefresherrefresh="refreshPrescriptionData">

      <view class="prescription_list pb10" wx:if="{{prescriptionList.length}}">
        <view class="bg-color-white m20 container-radius"
              wx:for="{{prescriptionList}}"
              wx:key="prescriptionId"
              data-item="{{item}}"
              bindtap="onPrescriptionItemClick">

          <!-- 顶部状态栏 -->
          <view class="flex_lr_m p20 bb1">
            <view class="flex_m">
              <view class="flex_c_m">
                <image class="w40 h40" src="{{static.ic_doctor_image}}" mode="aspectFit" />
              </view>
              <text class="f32 c333 b ml16">处方记录</text>
            </view>
            <view class="f28 c38bf87">已开药</view>
          </view>

          <!-- 详细信息 -->
          <view class="p20">
            <view class="f28 c333">就诊人：{{item.patientName || '-'}}</view>
            <view class="f28 c333 mt10">开药医生：{{item.doctorName || '-'}}</view>
            <view class="f28 c333 mt10">开药时间：{{item.createTime || '-'}}</view>
            <view class="f28 c333 mt10 flex" wx:if="{{item.medicineNames}}">
              <view class="c333 f28" style="white-space: nowrap">用药清单：</view>
              <view style="width: 100%;">
                <view class="c333 f28">{{item.medicineNames}}</view>
                <view class="c999 f22 mt10" wx:if="{{item.medicineCount}}">共{{item.medicineCount}}种药品</view>
              </view>
            </view>
          </view>

          <!-- 底部按钮 -->
          <view class="pl20 pr20 pb20 flex_c_end">
            <view class="f26 c666 pt10 pb10 pl20 pr20 container-radius"
                  style="border: 2rpx solid #E4E4E4"
                  data-action="prescription"
                  data-item="{{item}}"
                  catchtap="onActionClick">
              查看处方
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view wx:else class="flex_line_c no_msg_box">
        <image class="no_msg" src="{{static.nomes}}"></image>
        <view class="f28 c666">暂无处方记录</view>
      </view>

      <!-- 加载更多提示 -->
      <view class="load-more-tip" wx:if="{{prescriptionList.length > 0 && loading}}">
        <text class="f24 c999">加载中...</text>
      </view>
      <view class="load-more-tip" wx:if="{{prescriptionList.length > 0 && !hasMorePrescription && !loading}}">
        <text class="f24 c999">没有更多数据了</text>
      </view>
    </scroll-view>
  </view>

  <!-- 药品tab内容 -->
  <view class="tab-content" wx:if="{{mainTabIndex === 2}}">
    <view class="medicine-container">
      <view class="web-view-placeholder">
        <van-loading size="24px" color="#367DFF">加载中...</van-loading>
        <text class="placeholder-text">药品信息页面准备中...</text>
        <text class="placeholder-desc">此处将嵌入药品H5页面</text>
      </view>
      <!-- 实际项目中启用WebView -->
      <web-view wx:if="{{medicineWebViewUrl}}" 
                src="{{medicineWebViewUrl}}"
                bindmessage="onWebViewMessage">
      </web-view>
      <!-- <web-view src="https://patient-test.xiaonuohealth.com/#/"></web-view> -->
    </view>
  </view>
</view> 