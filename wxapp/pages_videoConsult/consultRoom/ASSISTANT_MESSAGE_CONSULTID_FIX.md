# 小助理消息 consultId 过滤问题修复

## 问题描述

小助理消息（type=10015）的 `consultId` 字段值为字符串 `'default'`，但在 `updateResult` 方法的第588行，代码使用了 `Number()` 进行数值比较：

```javascript
if (Number(message.consultId) !== Number(this.data.consultId)) {
  // 消息被错误过滤
  return
}
```

### 问题分析

1. **小助理消息的 consultId**: `'default'` (字符串)
2. **Number('default')**: 返回 `NaN`
3. **Number(实际consultId)**: 返回具体数值，如 `123456`
4. **NaN !== 123456**: 结果为 `true`，导致消息被过滤掉

## 修复方案

### 修改前的代码：
```javascript
if (Number(message.consultId) !== Number(this.data.consultId)) {
  console.log('consultId不匹配，忽略:', {
    messageConsultId: message.consultId,
    currentConsultId: this.data.consultId
  })
  return
}
```

### 修改后的代码：
```javascript
// 特殊处理小助理消息：consultId 为 'default' 的消息允许通过
// 对于其他消息，需要 consultId 匹配
if (message.consultId !== 'default' && Number(message.consultId) !== Number(this.data.consultId)) {
  console.log('consultId不匹配，忽略:', {
    messageConsultId: message.consultId,
    currentConsultId: this.data.consultId,
    messageType: message.type
  })
  return
}
```

## 修复逻辑

### 1. 特殊处理 consultId='default'
- 当 `message.consultId === 'default'` 时，直接允许消息通过
- 这样小助理消息就不会被过滤掉

### 2. 保持原有逻辑
- 对于其他消息（consultId 不是 'default'），仍然使用原有的数值比较逻辑
- 确保正常的视频咨询消息过滤机制不受影响

### 3. 增强调试信息
- 在日志中添加了 `messageType` 字段，便于调试时识别消息类型

## 消息流程

### 修复前：
```
小助理消息到达 
→ consultId: 'default' 
→ Number('default') = NaN 
→ NaN !== Number(this.data.consultId) = true 
→ 消息被过滤 ❌
```

### 修复后：
```
小助理消息到达 
→ consultId: 'default' 
→ message.consultId === 'default' = true 
→ 跳过 consultId 检查 
→ 消息正常处理 ✅
```

## 支持的消息类型

### consultId='default' 的消息：
- **type=10015**: 小助理问候语
- **type=10016**: 您发起了视频问诊
- 其他可能的系统消息

### 正常 consultId 的消息：
- **type=1**: 文本消息
- **type=2**: 图片消息
- **type=4**: 语音消息
- **type=16**: 处方消息
- **type=17**: 中药处方消息
- **type=10005**: 病历消息
- 其他业务消息

## 测试验证

### 1. 小助理消息测试
```javascript
// 模拟小助理消息
const assistantMessage = {
  consultId: 'default',
  consultType: 2,
  type: 10015,
  content: '我是您的招商信诺助理-小诺 健康问题请点击蓝色按钮【呼叫视频医生】',
  // ... 其他字段
}

// 验证消息是否能正常通过过滤
```

### 2. 正常消息测试
```javascript
// 模拟正常视频咨询消息
const normalMessage = {
  consultId: '123456',  // 与当前 consultId 匹配
  consultType: 2,
  type: 1,
  content: '正常的文本消息',
  // ... 其他字段
}

// 验证消息是否能正常通过过滤
```

### 3. 不匹配消息测试
```javascript
// 模拟不匹配的消息
const mismatchMessage = {
  consultId: '999999',  // 与当前 consultId 不匹配
  consultType: 2,
  type: 1,
  content: '不匹配的消息',
  // ... 其他字段
}

// 验证消息是否被正确过滤
```

## 调试信息

### 修复后的日志输出：
```javascript
// 小助理消息通过时
"处理视频问诊实时消息: {type: 10015, consultType: 2, consultId: 'default', ...}"

// 正常消息通过时
"处理视频问诊实时消息: {type: 1, consultType: 2, consultId: '123456', ...}"

// 消息被过滤时
"consultId不匹配，忽略: {messageConsultId: '999999', currentConsultId: '123456', messageType: 1}"
```

## 相关代码位置

- **文件**: `wxapp/pages_videoConsult/consultRoom/index.js`
- **方法**: `updateResult()`
- **行号**: 588-597 (修改后)

## 注意事项

### 1. 向后兼容
- 修改保持了对现有消息过滤逻辑的兼容性
- 只是为 consultId='default' 的消息添加了特殊处理

### 2. 安全性
- 只允许 consultId='default' 的消息跳过检查
- 其他消息仍然需要严格的 consultId 匹配

### 3. 扩展性
- 如果将来有其他特殊 consultId 的消息，可以在条件中添加
- 例如：`message.consultId !== 'default' && message.consultId !== 'system'`

## 业务影响

### 修复前：
- ❌ 小助理消息无法显示
- ❌ 用户看不到助理问候语
- ❌ 影响用户体验

### 修复后：
- ✅ 小助理消息正常显示
- ✅ 用户可以看到助理问候语和系统提示
- ✅ 提升用户体验
- ✅ 不影响其他消息的正常过滤

## 后续优化建议

### 1. 统一 consultId 处理
考虑在后端统一 consultId 的格式，避免字符串和数字混用

### 2. 消息类型分类
可以考虑为系统消息和业务消息使用不同的过滤逻辑

### 3. 配置化处理
将特殊 consultId 列表配置化，便于维护和扩展
