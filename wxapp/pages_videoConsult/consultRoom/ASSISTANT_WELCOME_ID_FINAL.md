# assistantWelcomeId 最终实现方案

## 修改概述

根据您的最终要求，完成了以下关键修改：
1. 接口只返回 `assistantWelcomeId`，不返回消息内容
2. 不使用默认的助理消息
3. 等待通过 IM 推送接收 type=10015 和 type=10016 的消息
4. 在模板中支持这两种新消息类型的渲染

## 核心修改内容

### 1. addInitialMessages 方法优化

#### 修改前的问题：
```javascript
// 接口返回空数组 []
const initialMessages = await this.getInitialMessagesFromAPI()

if (initialMessages && initialMessages.length > 0) {
  // 不会执行
} else {
  // 会执行，使用默认消息
  this.addDefaultInitialMessages()
}
```

#### 修改后的正确逻辑：
```javascript
try {
  // 调用后端接口获取 assistantWelcomeId
  await this.getInitialMessagesFromAPI()
  
  console.log('assistantWelcomeId 获取完成，不添加默认初始消息')
  console.log('等待通过 IM 推送接收 type=10015 和 type=10016 的消息')
  
  // 标记已处理初始消息，避免重复处理
  this.setData({
    hasInitialMessages: true
  })

} catch (err) {
  console.error('获取 assistantWelcomeId 失败:', err)
  console.log('assistantWelcomeId 获取失败，但不影响正常使用')
  
  // 即使获取失败也标记为已处理，避免重复调用
  this.setData({
    hasInitialMessages: true
  })
}
```

### 2. 接口响应处理

#### getInitialMessagesFromAPI 方法：
```javascript
if (res.data.code === 0) {
  console.log('初始消息接口调用成功:', res.data.data)
  
  // res.data.data 直接返回的就是 assistantWelcomeId
  const assistantWelcomeId = res.data.data
  
  // 保存 assistantWelcomeId 到页面数据中
  if (assistantWelcomeId) {
    this.setData({
      assistantWelcomeId: assistantWelcomeId
    })
    console.log('保存 assistantWelcomeId:', assistantWelcomeId)
  }
  
  // 返回空数组，但不会触发默认消息逻辑
  return []
}
```

### 3. 新增消息类型支持

#### 在 consult.wxml 模板中添加：
```xml
<!-- 小助理问候语 -->
<view wx:if="{{item.type == 10015}}" class="f28 c333 m30 p20 flex_c"
    style='background: #EAEAEA;border-radius: 8rpx;'>
    {{item.content}}
</view>

<!-- 您发起了视频问诊 -->
<view wx:if="{{item.type == 10016}}" class="f28 c333 m30 p20 flex_c"
    style='background: #EAEAEA;border-radius: 8rpx;'>
    {{item.content}}
</view>
```

#### 更新消息显示条件：
```xml
wx:if="{{item.relation == 1 && (...|| item.type == 10015 || item.type == 10016)}}"
```

#### 更新消息处理逻辑：
```javascript
// 历史消息和实时消息处理中都添加了新类型
if ([10005, 16, 17, 10006, 10007, 10008, 10009, 10010, 10011, 10012, 10013, 10015, 10016].includes(item.type)) {
  item.relation = 1 // 医生发送
}
```

## 完整数据流程

### 1. 页面初始化阶段
```
用户进入页面 
→ 调用 addInitialMessages() 
→ 调用 getInitialMessagesFromAPI() 
→ 获取并保存 assistantWelcomeId 
→ 标记 hasInitialMessages = true 
→ 不添加任何默认消息
```

### 2. IM 消息接收阶段
```
后端通过 IM 推送消息 
→ type=10015 (小助理问候语) 
→ type=10016 (您发起了视频问诊) 
→ 前端接收并显示这些消息
```

### 3. 发起视频咨询阶段
```
用户点击呼叫医生 
→ 调用 initiateVideoConsultation 
→ 将 assistantWelcomeId 传递给 videoPayInfo 接口 
→ 后端可以关联助理消息和视频咨询
```

## 接口规范

### 初始消息接口
- **接口地址**: `GET /user/ap/patient/consult/video/initial/messages`
- **请求参数**: 根据您的要求（具体参数待确认）
- **响应格式**:
  ```json
  {
    "code": 0,
    "msg": "success",
    "data": "welcome_12345"  // 直接返回 assistantWelcomeId 字符串
  }
  ```

### videoPayInfo 接口
- **新增参数**: `assistantWelcomeId`
- **参数示例**:
  ```json
  {
    "inquirerId": "12345",
    "price": 0,
    "conditionDesc": "无",
    "offlineDiagnosis": "无",
    "subOrderCode": "SUB123",
    "packageCode": "PKG456",
    "assistantWelcomeId": "welcome_12345"
  }
  ```

## 消息类型说明

### type=10015：小助理问候语
- **来源**: IM 推送
- **样式**: 灰色背景系统消息
- **relation**: 1 (医生发送)
- **内容**: 通过 `item.content` 显示

### type=10016：您发起了视频问诊
- **来源**: IM 推送
- **样式**: 灰色背景系统消息
- **relation**: 1 (医生发送)
- **内容**: 通过 `item.content` 显示

## 关键特性

### 1. 不使用默认消息
- ✅ 页面初始化时不显示任何硬编码的助理消息
- ✅ 完全依赖 IM 推送的消息内容
- ✅ 避免了消息重复或内容不一致的问题

### 2. assistantWelcomeId 关联
- ✅ 页面初始化时获取 assistantWelcomeId
- ✅ 发起视频咨询时传递给后端
- ✅ 建立助理消息和视频咨询的关联关系

### 3. 消息类型扩展
- ✅ 支持 type=10015 和 type=10016 消息类型
- ✅ 统一的系统消息样式
- ✅ 完整的消息处理逻辑

### 4. 错误处理
- ✅ assistantWelcomeId 获取失败不影响正常使用
- ✅ 避免重复调用初始消息接口
- ✅ 完善的异常处理机制

## 调试信息

在控制台中可以看到以下关键日志：

```javascript
// 页面初始化
"开始获取视频问诊初始助理消息"
"初始消息接口调用成功: welcome_12345"
"保存 assistantWelcomeId: welcome_12345"
"assistantWelcomeId 获取完成，不添加默认初始消息"
"等待通过 IM 推送接收 type=10015 和 type=10016 的消息"

// IM 消息接收
"视频问诊收到实时消息: {type: 10015, content: '...', relation: 1}"
"视频问诊收到实时消息: {type: 10016, content: '...', relation: 1}"

// 发起视频咨询
"添加 assistantWelcomeId 参数: welcome_12345"
```

## 测试要点

### 1. assistantWelcomeId 获取测试
```javascript
// 在控制台检查
const pages = getCurrentPages()
const currentPage = pages[pages.length - 1]
console.log('assistantWelcomeId:', currentPage.data.assistantWelcomeId)
console.log('hasInitialMessages:', currentPage.data.hasInitialMessages)
```

### 2. 消息显示测试
- 验证页面初始化时不显示默认消息
- 模拟 IM 推送 type=10015 消息，验证显示
- 模拟 IM 推送 type=10016 消息，验证显示

### 3. 参数传递测试
- 发起视频咨询时验证 assistantWelcomeId 参数传递

## 业务价值

### 1. 内容一致性
- 助理消息内容完全由后端控制
- 避免前端硬编码导致的内容不一致

### 2. 数据关联
- 通过 assistantWelcomeId 建立消息和咨询的关联
- 为数据分析和个性化推荐提供基础

### 3. 灵活性
- 支持动态配置助理消息内容
- 支持不同场景下的个性化消息

## 相关文件

- `wxapp/pages_videoConsult/consultRoom/index.js` - 主要逻辑修改
- `wxapp/pages_videoConsult/consultRoom/template/consult.wxml` - 模板渲染修改
- `wxapp/config/api.js` - 接口定义

## 注意事项

1. **IM 消息依赖**: 页面完全依赖 IM 推送的消息，确保 IM 系统正常工作
2. **时序控制**: 确保 assistantWelcomeId 获取在发起视频咨询之前完成
3. **错误处理**: assistantWelcomeId 获取失败不影响正常的视频咨询流程
4. **消息去重**: 避免重复显示相同的助理消息
