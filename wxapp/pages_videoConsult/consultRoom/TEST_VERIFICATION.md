# 视频问诊消息优化测试验证指南

## 测试环境准备

### 1. 测试数据准备
- 准备一个有历史消息的视频问诊会话
- 确保历史消息中包含病历消息(type=10005)和处方消息(type=16/17)
- 准备一个能发送实时IM消息的测试环境

### 2. 测试页面
- 视频咨询室页面：`pages_videoConsult/consultRoom/index`
- 图文咨询页面：`pages/consult/chat/chat`（用于验证兼容性）

## 测试用例

### 测试用例1：历史消息加载测试

**目标**：验证视频问诊页面能正确加载所有历史消息

**步骤**：
1. 从任意页面进入视频咨询室（不限制入口来源）
2. 观察页面是否正确加载历史消息
3. 检查是否包含病历和处方消息
4. 向上滚动加载更多历史消息

**预期结果**：
- ✅ 页面能正确显示所有历史消息
- ✅ 病历消息(type=10005)正确渲染
- ✅ 处方消息(type=16/17)正确渲染
- ✅ 滚动加载更多消息功能正常

**验证方法**：
```javascript
// 在控制台查看日志
console.log('视频咨询加载历史数据...')
console.log('UI元素高度查询结果:', res)
```

### 测试用例2：实时IM消息接收测试

**目标**：验证实时接收的IM消息能正确显示

**步骤**：
1. 进入视频咨询室页面
2. 通过后台或其他方式发送实时IM消息
3. 发送不同类型的消息：文本、图片、病历、处方
4. 观察消息是否实时显示

**预期结果**：
- ✅ 文本消息正确显示
- ✅ 图片消息正确显示
- ✅ 病历消息正确显示
- ✅ 处方消息正确显示
- ✅ 消息relation字段正确设置

**验证方法**：
```javascript
// 在控制台查看消息处理日志
console.log('视频问诊收到实时消息:', message)
console.log('添加消息到列表，relation已设置:', {...})
```

### 测试用例3：消息过滤测试

**目标**：验证消息过滤逻辑正确工作

**步骤**：
1. 在视频咨询室页面
2. 发送不同consultType的消息
3. 发送不同consultId的消息
4. 观察页面是否只显示匹配的消息

**预期结果**：
- ✅ 只显示consultType=2的消息
- ✅ 只显示匹配当前consultId的消息
- ✅ 其他消息被正确过滤

**验证方法**：
```javascript
// 查看过滤日志
console.log('非视频问诊消息，忽略:', message.consultType)
console.log('consultId不匹配，忽略:', {...})
```

### 测试用例4：图文问诊兼容性测试

**目标**：验证优化不影响图文问诊功能

**步骤**：
1. 进入图文咨询页面
2. 发送和接收消息
3. 检查缓存机制是否正常
4. 验证消息显示是否正常

**预期结果**：
- ✅ 图文问诊功能完全正常
- ✅ 缓存机制正常工作
- ✅ 消息分发正确

### 测试用例5：初始消息测试

**目标**：验证初始消息正确显示

**步骤**：
1. 清除相关缓存
2. 首次进入视频咨询室页面
3. 观察是否显示初始消息

**预期结果**：
- ✅ 显示小诺助理的欢迎消息
- ✅ 显示会员服务开通成功消息
- ✅ 消息格式正确

## 调试工具

### 1. 控制台日志监控
在浏览器控制台中监控以下关键日志：

```javascript
// 消息处理相关
"视频问诊收到实时消息:"
"处理视频问诊实时消息:"
"添加消息到列表，relation已设置:"

// 历史数据加载相关
"视频咨询加载历史数据..."
"视频咨询没有历史数据"

// 消息分发相关
"分发消息:"
"向视频咨询室页面分发消息:"

// 滚动高度计算相关
"开始计算滚动高度，当前状态:"
"滚动高度设置完成:"
```

### 2. 数据结构检查
检查关键数据结构：

```javascript
// 检查消息数组结构
console.log('messageArr:', this.data.messageArr)

// 检查单个消息结构
console.log('message structure:', {
  id: message.id,
  type: message.type,
  relation: message.relation,
  consultType: message.consultType,
  consultId: message.consultId,
  content: message.content
})
```

### 3. 页面状态检查
```javascript
// 检查页面状态
console.log('page state:', {
  showPatientInfo: this.data.showPatientInfo,
  fromHistoryList: this.data.fromHistoryList,
  consultId: this.data.consultId,
  doctorId: this.data.doctorId
})
```

## 常见问题排查

### 问题1：病历/处方消息不显示
**可能原因**：
- relation字段未正确设置
- 消息过滤逻辑过于严格
- 模板渲染条件不匹配

**排查方法**：
1. 检查消息的relation字段值
2. 检查consultType和consultId是否匹配
3. 检查模板中的wx:if条件

### 问题2：历史消息加载失败
**可能原因**：
- API接口参数错误
- 网络请求失败
- 数据格式不匹配

**排查方法**：
1. 检查API请求参数
2. 检查网络请求状态
3. 检查返回数据格式

### 问题3：实时消息不显示
**可能原因**：
- 消息分发逻辑错误
- 页面路由不匹配
- 消息过滤条件过严

**排查方法**：
1. 检查当前页面路由
2. 检查消息分发日志
3. 检查消息过滤条件

## 性能监控

### 1. 页面加载时间
监控视频咨询室页面的加载时间：
- 首次加载时间
- 历史数据获取时间
- 消息渲染时间

### 2. 内存使用
监控内存使用情况：
- 页面内存占用
- 消息数组大小
- 缓存使用情况

### 3. 用户体验
关注用户体验指标：
- 消息显示延迟
- 滚动流畅度
- 交互响应时间

## 回归测试清单

- [ ] 视频问诊基本功能正常
- [ ] 图文问诊功能不受影响
- [ ] 消息显示完整准确
- [ ] 实时消息接收正常
- [ ] 历史消息加载正常
- [ ] 页面性能无明显下降
- [ ] 错误处理机制有效
- [ ] 日志输出清晰有用
