# 视频咨询室页面返回问题测试指南

## 测试目标

验证从病历详情页面返回视频咨询室时，不会出现分页错误和重复加载数据的问题。

## 测试前准备

### 1. 开启调试模式
在浏览器控制台中，确保能看到以下关键日志：
```javascript
// 页面生命周期日志
"视频咨询室页面 onShow 触发"
"检查是否需要重新初始化聊天数据:"

// 分页状态日志
"重置分页状态，historyPage:"
"视频咨询加载历史数据..."

// 历史数据加载日志
"视频咨询没有历史数据" 或 "视频咨询历史数据加载成功"
```

### 2. 准备测试数据
- 确保测试账号有视频问诊历史记录
- 历史记录中包含病历消息（type=10005）
- 历史记录超过一页（便于测试分页逻辑）

## 核心测试用例

### 测试用例1：基本返回功能测试

**目标**：验证从病历详情返回时不会重复加载数据

**步骤**：
1. 进入视频咨询室页面
2. 等待历史消息完全加载
3. 记录当前 `historyPage` 值（应该是2或更大）
4. 点击任意病历消息，跳转到病历详情页
5. 在病历详情页停留几秒
6. 点击返回按钮回到视频咨询室

**预期结果**：
```javascript
// 控制台应该显示：
"视频咨询室页面 onShow 触发"
"检查是否需要重新初始化聊天数据: {needReinitChat: false, ...}"
"聊天数据已存在，无需重新初始化"

// 不应该看到：
"重置分页状态，historyPage: 1"  // 不应该重置
"视频咨询加载历史数据..."      // 不应该重新加载
```

**验证点**：
- ✅ 页面不闪烁
- ✅ 消息列表保持原有状态
- ✅ 滚动位置保持不变
- ✅ 没有网络请求发出

### 测试用例2：异常情况下的重新初始化测试

**目标**：验证在数据异常时能正确重新初始化

**步骤**：
1. 进入视频咨询室页面
2. 在控制台手动清空消息数据：
   ```javascript
   // 在控制台执行
   const pages = getCurrentPages()
   const currentPage = pages[pages.length - 1]
   currentPage.setData({messageArr: []})
   ```
3. 点击病历消息跳转到详情页
4. 点击返回按钮

**预期结果**：
```javascript
// 控制台应该显示：
"视频咨询室页面 onShow 触发"
"检查是否需要重新初始化聊天数据: {needReinitChat: true, ...}"
"需要重新初始化聊天数据"
"重置分页状态，historyPage: 1"
"视频咨询加载历史数据... {resetPagination: true, currentHistoryPage: 1}"
```

**验证点**：
- ✅ 页面重新加载数据
- ✅ historyPage 重置为 1
- ✅ 正确加载第一页数据
- ✅ 消息列表正常显示

### 测试用例3：多次跳转测试

**目标**：验证多次跳转返回的稳定性

**步骤**：
1. 进入视频咨询室页面
2. 点击病历消息A，跳转到详情页
3. 返回视频咨询室
4. 点击病历消息B，跳转到详情页
5. 返回视频咨询室
6. 重复步骤2-5多次

**预期结果**：
- ✅ 每次返回都不重新加载数据
- ✅ 页面状态保持稳定
- ✅ 没有内存泄漏或性能问题

### 测试用例4：滚动加载后返回测试

**目标**：验证滚动加载更多数据后返回的正确性

**步骤**：
1. 进入视频咨询室页面
2. 向上滚动，触发加载更多历史数据
3. 等待更多数据加载完成
4. 记录当前消息数量和 historyPage 值
5. 点击病历消息跳转到详情页
6. 返回视频咨询室

**预期结果**：
- ✅ 已加载的所有历史数据保持不变
- ✅ 消息数量不变
- ✅ historyPage 值保持不变
- ✅ 滚动位置大致保持不变

## 调试技巧

### 1. 监控页面状态
在控制台中实时查看页面状态：
```javascript
// 获取当前页面实例
const pages = getCurrentPages()
const currentPage = pages[pages.length - 1]

// 查看关键状态
console.log('当前页面状态:', {
  historyPage: currentPage.data.historyPage,
  messageArrLength: currentPage.data.messageArr ? currentPage.data.messageArr.length : 0,
  chatkey: currentPage.data.chatkey,
  loading: currentPage.loading,
  hasPrev: currentPage.data.hasPrev
})
```

### 2. 监控网络请求
在浏览器开发者工具的 Network 标签页中：
- 筛选 XHR/Fetch 请求
- 关注历史消息相关的 API 调用
- 验证是否有不必要的重复请求

### 3. 性能监控
在 Performance 标签页中：
- 记录页面返回时的性能表现
- 检查是否有不必要的重新渲染
- 监控内存使用情况

## 常见问题排查

### 问题1：返回时仍然重新加载数据

**可能原因**：
- `needReinitChat` 判断逻辑有误
- `messageArr` 被意外清空
- `chatkey` 发生变化

**排查方法**：
```javascript
// 在 onShow 方法中添加调试信息
console.log('调试信息:', {
  currentChatkey: this.data.chatkey,
  newChatkey: chatkey,
  messageArrExists: !!this.data.messageArr,
  messageArrLength: this.data.messageArr ? this.data.messageArr.length : 0,
  needReinitChat: needReinitChat
})
```

### 问题2：分页状态错误

**可能原因**：
- `resetPagination` 参数传递错误
- 分页重置逻辑未执行
- 并发请求导致状态混乱

**排查方法**：
```javascript
// 在 getHistoryData 方法开始处添加
console.log('getHistoryData 调用参数:', {
  isScroll: isScroll,
  resetPagination: resetPagination,
  currentHistoryPage: this.data.historyPage
})
```

### 问题3：页面状态不一致

**可能原因**：
- 页面生命周期管理有误
- 状态重置时机不对
- 异步操作时序问题

**排查方法**：
- 检查 `onShow`、`onHide`、`onUnload` 的调用时机
- 验证状态重置是否在正确的时机执行
- 检查异步操作的回调时序

## 回归测试清单

完成修复后，需要验证以下功能仍然正常：

- [ ] 视频咨询室页面首次进入正常
- [ ] 历史消息加载正常
- [ ] 实时消息接收正常
- [ ] 滚动加载更多功能正常
- [ ] 病历消息跳转功能正常
- [ ] 处方消息显示正常
- [ ] 图文咨询功能不受影响
- [ ] 页面性能无明显下降

## 测试报告模板

```
测试时间：[日期时间]
测试环境：[浏览器版本/设备信息]
测试人员：[姓名]

测试结果：
✅ 测试用例1：基本返回功能 - 通过
✅ 测试用例2：异常重新初始化 - 通过  
✅ 测试用例3：多次跳转 - 通过
✅ 测试用例4：滚动加载后返回 - 通过

发现问题：
[如有问题，详细描述]

建议：
[优化建议或注意事项]
```
