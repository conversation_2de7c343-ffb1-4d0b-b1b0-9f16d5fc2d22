# 视频咨询室页面返回问题修复

## 问题描述

当用户在视频咨询室页面点击病历消息跳转到病历详情页面，然后点击返回按钮回到视频咨询室时，会出现以下问题：

1. **分页状态错误**：`historyPage` 变成了第二页，导致重新加载时跳过第一页数据
2. **重复加载数据**：每次 `onShow` 都会重新初始化聊天数据
3. **用户体验差**：页面闪烁，数据重复加载

## 问题原因分析

### 1. 分页状态管理问题
```javascript
// 问题代码：在 getHistoryData 成功后递增页码
this.setData({
  historyPage: this.data.historyPage + 1  // 页码变成2
})

// 当从病历详情返回时，onShow 触发 initChatData
// initChatData 调用 getHistoryData，但此时 historyPage = 2
// 导致请求第二页数据，跳过了第一页
```

### 2. 页面生命周期管理问题
```javascript
// 问题代码：onShow 总是重新初始化
async onShow() {
  this.initChatData()  // 每次都重新加载
}
```

## 解决方案

### 1. 优化页面生命周期管理

#### 修改前：
```javascript
async onShow() {
  if (this.data.consultId && this.data.doctorId) {
    this.setData({
      chatkey: 'video_' + this.data.patientId + '_' + this.data.doctorId + '_' + this.data.consultId
    }, () => {
      this.initChatData()  // 总是重新初始化
    })
  }
}
```

#### 修改后：
```javascript
async onShow() {
  console.log('视频咨询室页面 onShow 触发')
  
  if (this.data.consultId && this.data.doctorId) {
    const chatkey = 'video_' + this.data.patientId + '_' + this.data.doctorId + '_' + this.data.consultId
    
    // 检查是否需要重新初始化聊天数据
    const needReinitChat = !this.data.chatkey || 
                          this.data.chatkey !== chatkey || 
                          !this.data.messageArr || 
                          this.data.messageArr.length === 0

    if (needReinitChat) {
      console.log('需要重新初始化聊天数据')
      this.setData({ chatkey: chatkey }, () => {
        this.initChatData()
      })
    } else {
      console.log('聊天数据已存在，无需重新初始化')
    }
  }
}
```

### 2. 重置分页状态

#### 修改前：
```javascript
initChatData() {
  this.data.hasPrev = true
  this.setData({
    messageArr: [],
    previewList: []
  })
  this.getHistoryData()  // 使用当前的 historyPage（可能是2）
}
```

#### 修改后：
```javascript
initChatData() {
  console.log('视频问诊初始化聊天数据 - 不使用本地缓存，直接获取历史数据')
  
  // 重置分页状态，避免从其他页面返回时页码错误
  this.data.hasPrev = true
  this.data.historyPage = 1
  this.loading = false
  
  this.setData({
    messageArr: [],
    previewList: [],
    historyPage: 1
  })
  
  console.log('重置分页状态，historyPage:', this.data.historyPage)
  
  // 传入 resetPagination=true 表示重置分页状态
  this.getHistoryData(false, true)
}
```

### 3. 增强历史数据获取方法

#### 修改前：
```javascript
getHistoryData(isScroll) {
  // 直接使用当前的 historyPage
  const params = {
    page: this.data.historyPage,
    // ...
  }
}
```

#### 修改后：
```javascript
getHistoryData(isScroll = false, resetPagination = false) {
  console.log('视频咨询加载历史数据...', {
    isScroll,
    resetPagination,
    currentHistoryPage: this.data.historyPage,
    // ...
  })

  // 如果需要重置分页状态（通常在重新初始化时）
  if (resetPagination) {
    console.log('重置分页状态')
    this.data.historyPage = 1
    this.data.hasPrev = true
    this.loading = false
  }
  
  // 使用重置后的页码
  const params = {
    page: this.data.historyPage,
    // ...
  }
}
```

### 4. 添加页面状态管理

```javascript
onUnload() {
  console.log('视频咨询室页面 onUnload 触发')
  // 页面卸载时清理状态
  this.resetPageState()
}

/**
 * 重置页面状态
 * 在页面卸载或需要重新初始化时调用
 */
resetPageState() {
  console.log('重置视频咨询室页面状态')
  this.data.historyPage = 1
  this.data.hasPrev = true
  this.loading = false
}
```

## 修复效果

### 修复前的问题流程：
1. 用户在视频咨询室页面（historyPage = 1）
2. 点击病历消息，跳转到病历详情页
3. 历史数据加载完成，historyPage 变成 2
4. 用户点击返回按钮
5. onShow 触发，重新调用 initChatData
6. getHistoryData 使用 historyPage = 2，跳过第一页数据 ❌

### 修复后的正确流程：
1. 用户在视频咨询室页面（historyPage = 1）
2. 点击病历消息，跳转到病历详情页
3. 历史数据加载完成，historyPage 变成 2
4. 用户点击返回按钮
5. onShow 触发，检查是否需要重新初始化
6. 如果聊天数据已存在，不重新加载 ✅
7. 如果需要重新加载，重置 historyPage = 1 ✅

## 测试用例

### 测试用例1：正常返回不重复加载
**步骤**：
1. 进入视频咨询室页面
2. 等待历史消息加载完成
3. 点击病历消息跳转到详情页
4. 点击返回按钮

**预期结果**：
- ✅ 页面不重新加载历史数据
- ✅ 消息列表保持原有状态
- ✅ 滚动位置保持不变

### 测试用例2：页面重新初始化
**步骤**：
1. 进入视频咨询室页面
2. 清除页面数据（模拟异常情况）
3. 点击病历消息跳转到详情页
4. 点击返回按钮

**预期结果**：
- ✅ 页面重新初始化聊天数据
- ✅ historyPage 重置为 1
- ✅ 正确加载第一页数据

### 测试用例3：滚动加载更多
**步骤**：
1. 进入视频咨询室页面
2. 向上滚动加载更多历史数据
3. 点击病历消息跳转到详情页
4. 点击返回按钮

**预期结果**：
- ✅ 页面不重新加载
- ✅ 已加载的历史数据保持不变
- ✅ 分页状态保持正确

## 调试信息

在控制台中可以看到以下关键日志：

```javascript
// 页面生命周期
"视频咨询室页面 onShow 触发"
"检查是否需要重新初始化聊天数据: {needReinitChat: false, ...}"
"聊天数据已存在，无需重新初始化"

// 分页状态管理
"重置分页状态，historyPage: 1"
"视频咨询加载历史数据... {resetPagination: true, currentHistoryPage: 1}"

// 状态重置
"重置视频咨询室页面状态"
```

## 注意事项

1. **兼容性**：修改保持向后兼容，不影响现有功能
2. **性能**：避免不必要的数据重新加载，提升用户体验
3. **状态管理**：确保页面状态在各种场景下都能正确管理
4. **调试支持**：添加详细日志，便于问题排查

## 相关文件

- `wxapp/pages_videoConsult/consultRoom/index.js` - 主要修改文件
- `wxapp/pages_videoConsult/consultRoom/PAGE_RETURN_FIX.md` - 本文档
