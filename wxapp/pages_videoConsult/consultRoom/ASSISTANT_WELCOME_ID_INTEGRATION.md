# assistantWelcomeId 集成说明

## 修改概述

根据您的要求，修改了初始助理消息接口的处理逻辑，添加了 `assistantWelcomeId` 标识的获取和传递功能。

## 主要修改内容

### 1. 页面数据结构修改

在 `wxapp/pages_videoConsult/consultRoom/index.js` 的 `data` 中添加了新字段：

```javascript
data: {
  // ... 其他字段
  assistantWelcomeId: null, //助理欢迎消息ID，用于发起视频咨询时传递给后端
  // ... 其他字段
}
```

### 2. 初始消息接口响应格式调整

#### 修改前的响应格式：
```json
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "id": "msg_001",
      "content": "助理消息内容",
      // ... 其他消息字段
    }
  ]
}
```

#### 修改后的响应格式：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "assistantWelcomeId": "welcome_12345",
    "messages": [
      {
        "id": "msg_001",
        "content": "助理消息内容",
        // ... 其他消息字段
      }
    ]
  }
}
```

### 3. 前端处理逻辑修改

#### `getInitialMessagesFromAPI()` 方法修改：

```javascript
// 修改前：
return res.data.data || []

// 修改后：
const responseData = res.data.data || {}

// 保存 assistantWelcomeId
if (responseData.assistantWelcomeId) {
  this.setData({
    assistantWelcomeId: responseData.assistantWelcomeId
  })
  console.log('保存 assistantWelcomeId:', responseData.assistantWelcomeId)
}

// 返回消息数组
return responseData.messages || []
```

### 4. 发起视频咨询时传递 assistantWelcomeId

#### `initiateVideoConsultation()` 方法修改：

```javascript
// 构建 videoPayInfo API 参数
const params = {
  inquirerId: currentPatient.inquirerId,
  price: 0,
  conditionDesc: '无',
  offlineDiagnosis: '无',
  subOrderCode: this.data.subOrderCode,
  packageCode: this.data.packageCode
}

// 添加 assistantWelcomeId 参数（如果有的话）
if (this.data.assistantWelcomeId) {
  params.assistantWelcomeId = this.data.assistantWelcomeId
  console.log('添加 assistantWelcomeId 参数:', this.data.assistantWelcomeId)
}
```

## 数据流程

### 1. 获取助理消息阶段
```
用户进入页面 
→ 调用初始消息接口 
→ 后端返回 {assistantWelcomeId, messages} 
→ 前端保存 assistantWelcomeId 到页面数据 
→ 显示助理消息
```

### 2. 发起视频咨询阶段
```
用户点击呼叫医生 
→ 调用 initiateVideoConsultation 
→ 将 assistantWelcomeId 添加到请求参数 
→ 调用 videoPayInfo 接口 
→ 后端可以关联助理消息和视频咨询
```

## 接口参数说明

### 初始消息接口
- **接口地址**: `GET /user/ap/patient/consult/video/initial/messages`
- **入参**: 根据您的修改要求（请提供具体参数）
- **出参**: 
  ```json
  {
    "code": 0,
    "data": {
      "assistantWelcomeId": "welcome_12345",
      "messages": [...]
    }
  }
  ```

### videoPayInfo 接口
- **新增参数**: `assistantWelcomeId` (可选)
- **参数说明**: 助理欢迎消息的唯一标识，用于后端关联助理消息和视频咨询

## 错误处理

### 1. assistantWelcomeId 缺失
如果初始消息接口没有返回 `assistantWelcomeId`，前端会：
- 正常显示助理消息
- 在发起视频咨询时不传递 `assistantWelcomeId` 参数
- 不影响正常的视频咨询流程

### 2. 接口格式兼容
代码支持以下两种响应格式：
```javascript
// 新格式（推荐）
{
  "data": {
    "assistantWelcomeId": "xxx",
    "messages": [...]
  }
}

// 降级格式（兼容）
{
  "data": []  // 直接是消息数组
}
```

## 调试信息

在控制台中可以看到以下关键日志：

```javascript
// 获取助理消息时
"初始消息接口调用成功: {assistantWelcomeId: 'xxx', messages: [...]}"
"保存 assistantWelcomeId: welcome_12345"

// 发起视频咨询时
"添加 assistantWelcomeId 参数: welcome_12345"
"Step 1: 调用 videoPayInfo API，参数: {assistantWelcomeId: 'welcome_12345', ...}"
```

## 测试要点

### 1. 正常流程测试
1. 进入视频咨询室页面
2. 验证是否正确获取和保存 `assistantWelcomeId`
3. 点击呼叫医生
4. 验证 `videoPayInfo` 接口是否包含 `assistantWelcomeId` 参数

### 2. 兼容性测试
1. 测试后端返回旧格式时的兼容性
2. 测试 `assistantWelcomeId` 缺失时的处理
3. 测试接口调用失败时的降级处理

### 3. 数据验证
```javascript
// 在控制台检查页面状态
const pages = getCurrentPages()
const currentPage = pages[pages.length - 1]
console.log('assistantWelcomeId:', currentPage.data.assistantWelcomeId)
```

## 后端实现建议

### 1. 数据库设计
建议在助理消息表中添加唯一标识字段：
```sql
ALTER TABLE video_consult_initial_messages 
ADD COLUMN welcome_id VARCHAR(50) UNIQUE;
```

### 2. 接口实现
- 为每次助理消息请求生成唯一的 `assistantWelcomeId`
- 在 `videoPayInfo` 接口中接收并处理 `assistantWelcomeId` 参数
- 建立助理消息和视频咨询的关联关系

### 3. 业务价值
通过 `assistantWelcomeId` 可以：
- 追踪用户从看到助理消息到发起视频咨询的完整流程
- 分析不同助理消息内容的转化效果
- 为个性化推荐提供数据支持

## 相关文件

- `wxapp/pages_videoConsult/consultRoom/index.js` - 主要修改文件
- `wxapp/config/api.js` - 接口定义文件
- `INITIAL_MESSAGES_API_SPEC.md` - 接口规范文档（已更新）

## 注意事项

1. **向后兼容**: 代码保持对旧接口格式的兼容性
2. **可选参数**: `assistantWelcomeId` 是可选参数，不影响现有流程
3. **错误处理**: 完善的异常处理，确保功能稳定
4. **调试支持**: 详细的日志输出，便于问题排查

## 下一步工作

1. 请提供初始消息接口的具体入参要求
2. 后端实现相应的接口逻辑
3. 进行完整的功能测试
4. 验证数据关联的正确性
