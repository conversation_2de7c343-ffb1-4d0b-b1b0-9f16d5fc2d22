# 待接诊状态呼叫医生功能测试指南

## 测试目标

验证在待接诊状态下，点击呼叫医生按钮能够正确调用 `api.startVideoCall` 接口，并在成功后跳转到呼叫页面。

## 测试前准备

### 1. 环境准备
- 确保有待接诊状态的视频问诊记录
- 确保网络连接正常
- 确保用户已登录且有有效的患者ID

### 2. 数据准备
- 准备一个处于待接诊状态的视频咨询记录
- 确保 `consultId` 和 `patientId` 有效
- 确保 `isWaitingStatus` 为 true

### 3. 调试准备
在浏览器控制台中开启网络监控，关注以下接口调用：
- `api.startVideoCall` 接口调用
- 页面跳转日志

## 核心测试用例

### 测试用例1：正常呼叫流程测试

**目标**：验证正常情况下的呼叫流程

**前置条件**：
- 页面处于待接诊状态（`isWaitingStatus: true`）
- 用户信息和问诊信息完整

**测试步骤**：
1. 进入视频咨询室页面
2. 确认页面显示"呼叫医生"按钮
3. 点击"呼叫医生"按钮
4. 观察loading状态显示
5. 等待接口调用完成
6. 观察页面跳转

**预期结果**：
```javascript
// 控制台日志应显示：
"待接诊状态，调用视频呼叫接口"
"调用视频拨号接口，参数: {patientId: '...', videoConsultId: '...'}"
"视频拨号接口调用成功: {...}"
"提取的医生信息: {...}"
"存储医生信息到全局数据: {...}"
"跳转到呼叫医生页面: /pages_videoConsult/callDoctor/index?..."
"成功跳转到呼叫医生页面"
```

**验证点**：
- ✅ 显示loading状态："发起视频通话..."
- ✅ 调用 `api.startVideoCall` 接口
- ✅ 接口参数正确：`{patientId, videoConsultId}`
- ✅ 成功跳转到呼叫医生页面
- ✅ URL参数包含必要信息
- ✅ 医生信息存储到全局数据

### 测试用例2：接口调用失败测试

**目标**：验证接口调用失败时的错误处理

**测试步骤**：
1. 模拟网络异常或接口返回错误
2. 点击"呼叫医生"按钮
3. 观察错误处理

**模拟方法**：
```javascript
// 在控制台临时修改接口地址来模拟失败
const originalRequest = util.request
util.request = function(url, params, method, version) {
  if (url.includes('startVideoCall')) {
    return Promise.resolve({
      data: {
        code: -1,
        msg: '测试错误信息'
      }
    })
  }
  return originalRequest.apply(this, arguments)
}
```

**预期结果**：
- ✅ 隐藏loading状态
- ✅ 显示错误提示："测试错误信息"
- ✅ 不跳转页面
- ✅ 控制台显示错误日志

### 测试用例3：网络异常测试

**目标**：验证网络异常时的处理

**测试步骤**：
1. 断开网络连接
2. 点击"呼叫医生"按钮
3. 观察异常处理

**预期结果**：
- ✅ 隐藏loading状态
- ✅ 显示错误提示："网络异常，请稍后重试"
- ✅ 不跳转页面
- ✅ 控制台显示异常日志

### 测试用例4：参数验证测试

**目标**：验证必要参数的验证逻辑

**测试步骤**：
1. 在控制台临时清空患者ID：
   ```javascript
   const pages = getCurrentPages()
   const currentPage = pages[pages.length - 1]
   currentPage.setData({patientId: ''})
   ```
2. 点击"呼叫医生"按钮

**预期结果**：
- ✅ 显示错误提示："用户信息异常，请重新登录"
- ✅ 不调用接口
- ✅ 不跳转页面

### 测试用例5：医生信息提取测试

**目标**：验证医生信息的正确提取和存储

**测试步骤**：
1. 正常调用接口
2. 在控制台检查全局数据：
   ```javascript
   const app = getApp()
   console.log('全局医生信息:', app.globalData.videoCallDoctorInfo)
   ```
3. 检查URL参数是否包含医生信息

**预期结果**：
- ✅ 全局数据中包含医生信息
- ✅ 医生信息字段完整（name, title, avatar等）
- ✅ URL参数包含编码后的医生信息

## 调试技巧

### 1. 监控接口调用
在浏览器开发者工具的Network标签页中：
- 筛选XHR/Fetch请求
- 查找 `startVideoCall` 接口调用
- 检查请求参数和响应数据

### 2. 检查页面状态
```javascript
// 在控制台检查当前页面状态
const pages = getCurrentPages()
const currentPage = pages[pages.length - 1]
console.log('页面状态:', {
  isWaitingStatus: currentPage.data.isWaitingStatus,
  patientId: currentPage.data.patientId,
  consultId: currentPage.data.consultId,
  doctorId: currentPage.data.doctorId
})
```

### 3. 检查全局数据
```javascript
// 检查医生信息是否正确存储
const app = getApp()
console.log('全局医生信息:', app.globalData.videoCallDoctorInfo)
```

### 4. 模拟不同响应数据
```javascript
// 模拟不同的医生信息字段
const mockResponse = {
  data: {
    code: 0,
    data: {
      doctorName: '测试医生',
      doctorTitle: '主任医师',
      doctorHeadUrl: 'https://example.com/avatar.jpg',
      doctorExpertise: '内科专家',
      goodRate: '98%',
      serviceCount: 1000
    }
  }
}
```

## 常见问题排查

### 问题1：接口调用失败
**可能原因**：
- 参数格式错误
- 接口地址错误
- 权限验证失败

**排查方法**：
1. 检查接口参数格式
2. 检查用户登录状态
3. 检查接口版本号

### 问题2：页面跳转失败
**可能原因**：
- URL格式错误
- 页面路径不存在
- 参数编码问题

**排查方法**：
1. 检查跳转URL格式
2. 验证目标页面是否存在
3. 检查参数是否正确编码

### 问题3：医生信息丢失
**可能原因**：
- 字段映射错误
- 全局数据存储失败
- URL参数传递失败

**排查方法**：
1. 检查API响应数据结构
2. 验证字段映射逻辑
3. 检查全局数据存储

## 性能测试

### 1. 响应时间测试
- 记录从点击按钮到页面跳转的时间
- 目标：< 3秒

### 2. 内存使用测试
- 监控页面内存使用情况
- 确保没有内存泄漏

### 3. 网络请求测试
- 监控网络请求数量和大小
- 确保请求效率

## 回归测试清单

完成修改后，需要验证以下功能仍然正常：

- [ ] 非待接诊状态的呼叫医生功能
- [ ] 视频咨询室其他功能
- [ ] 呼叫医生页面功能
- [ ] 医生信息显示
- [ ] 页面跳转逻辑
- [ ] 错误处理机制

## 测试报告模板

```
测试时间：[日期时间]
测试环境：[浏览器版本/设备信息]
测试人员：[姓名]

测试结果：
✅ 测试用例1：正常呼叫流程 - 通过
✅ 测试用例2：接口调用失败 - 通过
✅ 测试用例3：网络异常 - 通过
✅ 测试用例4：参数验证 - 通过
✅ 测试用例5：医生信息提取 - 通过

发现问题：
[如有问题，详细描述]

性能表现：
- 响应时间：[X]秒
- 内存使用：正常
- 网络请求：高效

建议：
[优化建议或注意事项]
```
