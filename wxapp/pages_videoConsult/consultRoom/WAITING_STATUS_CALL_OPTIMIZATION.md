# 待接诊状态呼叫医生逻辑优化

## 优化概述

优化了 `handleWaitingStatusCall` 方法，使其在待接诊状态下直接调用 `api.startVideoCall` 接口，成功后跳转到呼叫页面，而不是直接跳转页面。

## 修改内容

### 1. 主要方法优化

#### 修改前：直接跳转页面
```javascript
handleWaitingStatusCall() {
  console.log('待接诊状态，直接跳转到呼叫医生页面')
  
  // 构建跳转URL
  let url = `/pages_videoConsult/callDoctor/index?consultId=${this.data.consultId}`
  
  // 添加医生ID（如果有）
  if (this.data.doctorId) {
    url += `&doctorId=${this.data.doctorId}`
  }
  
  // 直接跳转到呼叫医生页面
  wx.navigateTo({ url: url })
}
```

#### 修改后：调用接口后跳转
```javascript
async handleWaitingStatusCall() {
  console.log('待接诊状态，调用视频呼叫接口')
  
  try {
    // 显示loading状态
    wx.showLoading({
      title: '发起视频通话...',
      mask: true
    })

    // 构建接口参数
    const params = {
      patientId: this.data.patientId,
      videoConsultId: this.data.consultId
    }

    // 调用视频拨号接口
    const res = await util.request(api.startVideoCall, params, 'post', '2')

    if (res.data.code === 0) {
      // 提取医生信息
      const doctorInfo = this.extractDoctorInfo(res.data.data)
      
      // 存储医生信息到全局数据
      const app = getApp()
      if (app.globalData) {
        app.globalData.videoCallDoctorInfo = doctorInfo
      }

      // 构建跳转URL并跳转
      let url = `/pages_videoConsult/callDoctor/index?videoConsultId=${params.videoConsultId}&patientId=${params.patientId}`
      // 添加其他参数...
      
      wx.navigateTo({ url: url })
    } else {
      // 处理接口调用失败
      wx.showToast({
        title: res.data.msg || '发起视频通话失败',
        icon: 'none'
      })
    }
  } catch (err) {
    // 处理异常
    wx.showToast({
      title: '网络异常，请稍后重试',
      icon: 'none'
    })
  }
}
```

### 2. 新增辅助方法

#### `extractDoctorInfo(responseData)`
从API响应中提取医生信息的方法：

```javascript
extractDoctorInfo(responseData) {
  // 定义可能的医生信息字段映射
  const fieldMappings = {
    avatar: ['doctorHeadUrl', 'doctorPhoto', 'headUrl', 'avatar', 'photo'],
    name: ['doctorName', 'name', 'realName'],
    title: ['doctorTitle', 'title', 'jobTitle', 'position'],
    specialty: ['doctorExpertise', 'expertise', 'specialty', 'specialization'],
    goodRate: ['goodRate', 'rating', 'score'],
    serviceCount: ['serviceCount', 'consultCount', 'count']
  }

  const doctorInfo = {}
  
  // 遍历字段映射，提取医生信息
  Object.keys(fieldMappings).forEach(targetField => {
    const possibleFields = fieldMappings[targetField]
    for (const field of possibleFields) {
      if (responseData[field] !== undefined && responseData[field] !== null && responseData[field] !== '') {
        doctorInfo[targetField] = responseData[field]
        break
      }
    }
  })

  return doctorInfo
}
```

#### `addDoctorInfoToUrl(baseUrl, doctorInfo)`
将医生信息添加到URL参数中的方法：

```javascript
addDoctorInfoToUrl(baseUrl, doctorInfo) {
  let url = baseUrl

  // 添加医生信息到URL参数（进行URL编码）
  if (doctorInfo.name) {
    url += `&doctorName=${encodeURIComponent(doctorInfo.name)}`
  }
  if (doctorInfo.title) {
    url += `&doctorTitle=${encodeURIComponent(doctorInfo.title)}`
  }
  // ... 其他字段

  return url
}
```

## 接口参数说明

### `api.startVideoCall` 接口

**请求参数：**
```javascript
{
  patientId: string,      // 患者ID
  videoConsultId: string  // 视频咨询ID
}
```

**响应数据：**
```javascript
{
  code: 0,
  data: {
    // 医生信息字段（字段名可能有变化）
    doctorName: string,        // 医生姓名
    doctorHeadUrl: string,     // 医生头像
    doctorTitle: string,       // 医生职称
    doctorExpertise: string,   // 医生专长
    goodRate: string,          // 好评率
    serviceCount: number,      // 服务次数
    // ... 其他可能的字段
  }
}
```

## 优化效果

### 1. 流程优化
**修改前：**
1. 用户点击呼叫医生
2. 直接跳转到呼叫页面
3. 呼叫页面再调用接口

**修改后：**
1. 用户点击呼叫医生
2. 调用 `api.startVideoCall` 接口
3. 接口成功后跳转到呼叫页面
4. 呼叫页面直接使用已获取的医生信息

### 2. 用户体验提升
- ✅ 提前验证接口调用是否成功
- ✅ 显示loading状态，用户体验更好
- ✅ 错误处理更完善，失败时不会跳转页面
- ✅ 医生信息提前获取，呼叫页面加载更快

### 3. 数据流优化
- ✅ 医生信息存储到全局数据，供呼叫页面使用
- ✅ URL参数作为备用方案，确保数据传递可靠
- ✅ 支持多种可能的字段名，兼容性更好

## 错误处理

### 1. 参数验证
```javascript
// 验证必要参数
if (!params.patientId) {
  wx.showToast({
    title: '用户信息异常，请重新登录',
    icon: 'none'
  })
  return
}

if (!params.videoConsultId) {
  wx.showToast({
    title: '问诊信息异常，请重试',
    icon: 'none'
  })
  return
}
```

### 2. 接口调用失败处理
```javascript
if (res.data.code === 0) {
  // 成功处理
} else {
  console.error('视频拨号接口调用失败:', res.data.msg)
  wx.showToast({
    title: res.data.msg || '发起视频通话失败',
    icon: 'none',
    duration: 3000
  })
}
```

### 3. 网络异常处理
```javascript
catch (err) {
  console.error('视频拨号接口调用异常:', err)
  wx.hideLoading()
  wx.showToast({
    title: '网络异常，请稍后重试',
    icon: 'none',
    duration: 3000
  })
}
```

## 测试要点

### 1. 功能测试
- [ ] 待接诊状态下点击呼叫医生按钮
- [ ] 验证是否显示loading状态
- [ ] 验证接口调用是否成功
- [ ] 验证成功后是否正确跳转到呼叫页面

### 2. 错误场景测试
- [ ] 网络异常时的处理
- [ ] 接口返回错误时的处理
- [ ] 参数缺失时的处理

### 3. 数据传递测试
- [ ] 医生信息是否正确存储到全局数据
- [ ] URL参数是否正确传递
- [ ] 呼叫页面是否能正确获取医生信息

## 调试信息

在控制台中可以看到以下关键日志：

```javascript
// 方法调用
"待接诊状态，调用视频呼叫接口"
"调用视频拨号接口，参数: {patientId: '...', videoConsultId: '...'}"

// 接口响应
"视频拨号接口调用成功: {...}"
"startVideoCall API 完整响应数据结构: {...}"

// 医生信息处理
"提取医生信息结果: {name: '...', title: '...', ...}"
"存储医生信息到全局数据: {...}"

// 页面跳转
"跳转到呼叫医生页面: /pages_videoConsult/callDoctor/index?..."
"成功跳转到呼叫医生页面"
```

## 兼容性说明

1. **向后兼容**：修改不影响其他调用方式
2. **字段兼容**：支持多种可能的医生信息字段名
3. **错误兼容**：完善的错误处理机制
4. **数据兼容**：同时使用全局数据和URL参数传递信息

## 相关文件

- `wxapp/pages_videoConsult/consultRoom/index.js` - 主要修改文件
- `wxapp/config/api.js` - 接口定义文件
- `wxapp/pages_videoConsult/callDoctor/index.js` - 目标跳转页面
