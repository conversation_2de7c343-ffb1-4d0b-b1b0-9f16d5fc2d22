<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"
	pageNum="{{pageNum}}"></navbar>
<view class='chating-wrapper' catchtap='chatingWrapperClick'>
	<!-- 就诊人信息区域 -->
	<view id='patientBox' class="patient-info-box bg-color-white" style="top:{{44+statusBarHeight}}px" wx:if="{{showPatientInfo}}">
		<view class="patient-info-header">
			<text class="patient-title">就诊人信息</text>
			<text class="required-mark">*</text>
		</view>

		<!-- 就诊人滑动区域 -->
		<scroll-view class="patient-scroll" scroll-x="true"
			show-scrollbar="false"
			scroll-left="{{patientScrollLeft}}"
			scroll-with-animation="{{true}}"
			wx:if="{{patientList.length > 0}}"
			>
			<view class="patient-scroll-content">
				<view wx:for="{{patientList}}" wx:key="inquirerId" class="patient-item {{currentPatientIndex == index ? 'active' : ''}}" bindtap="selectPatient" data-index="{{index}}">
					<view class="patient-content">
						<view class="patient-main-info">
							<text class="patient-name">{{item.name}}</text>
							<image class="edit-icon" src="{{ic_patient_edit}}" bindtap="editPatient" data-index="{{index}}"></image>
						</view>
						<view class="patient-detail-info" wx:if="{{item.isInfoComplete}}">
							<text class="patient-gender">{{item.gender == 1 ? '男' : (item.gender == 0 ? '女' : '未知')}}</text>
							<text class="patient-gender">|</text>
							<text class="patient-age" wx:if="{{item.age}}">{{item.age}}岁</text>
							<text class="patient-age">|</text>
							<text class="patient-relation">{{item.relationName}}</text>
						</view>
						<view class="patient-detail-supplement" wx:else>信息待补充</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 我已阅读并同意 互联网问诊知情同意书 -->
		<van-checkbox value="{{ checked }}" bind:change="onChecked" data-type="checked" icon-size="28rpx"
				custom-class="dib"><text class="c666 f24">我已阅读并同意</text><text class="f24 color-primary" data-type='6'
				bind:tap="handleShowAgm">《互联网问诊知情同意书》</text>
		</van-checkbox>

		<!-- 呼叫视频医生按钮 -->
		<view class="call-doctor-btn" bindtap="callVideoDoctor">
			<text class="call-btn-text">+ 呼叫视频医生</text>
		</view>
	</view>
	
	
	<!-- 医生信息 -->
	<view id='dobBox' wx:if="{{doctorInfo.name}}" class="dob-box bg-color-white"
		style="z-index:9999;top:{{44+statusBarHeight+(showPatientInfo ? 200 : 0)}}px">
		<view class="flex_m" hover-class="none" hover-stop-propagation="false">
			<view class="doc-photo">
				<image mode="aspectFill" src="{{doctorInfo.photo ? doctorInfo.photo : '/static/images/doctor_icon.png'}}"
					lazy-load="false">
				</image>
			</view>
			<view class="doc-content flex_m ml10">
				<view class="flex_m">
					<view class="f28 c333 b">
						{{doctorInfo.name}}
					</view>
					<view class="f24 c666 ml15" style="margin-top: 5rpx;">
						{{doctorInfo.department }}｜{{doctorInfo.title}}
					</view>
				</view>
			</view>
		</view>
		<view class="call-doctor-btn" bindtap="callVideoDoctor" wx:if="{{isWaitingStatus}}">
			<text class="call-btn-text">+ 呼叫视频医生</text>
		</view>
	</view>
	<!-- 聊天模块 -->
	<import src='./template/consult.wxml' />
		<template is='consult'
		data="{{messageArr,baseUrl,previewList,doctorId,scrollIntoView,doctorInfo,scrollHeight,scrollTop,avatar,static}}"></template>

	<!-- 服务记录按钮 -->
	<view class="service-record-btn" bindtap="goServiceRecord">
		<image src="{{ic_service_record}}" mode="aspectFit"></image>
	</view>

  	<!-- 互联网问诊知情同意书 -->
   <agreement-popup id="ageePopup" type='6' bind:agreement='onAgreement' bind:closePopup='onclosePopup'></agreement-popup>
</view>