# 新增消息类型集成说明

## 修改概述

根据您的要求，完成了以下修改：
1. 修正了 `assistantWelcomeId` 的获取逻辑（`res.data.data` 直接返回 `assistantWelcomeId`）
2. 在 `consult.wxml` 模板中新增了两个消息类型的渲染支持
3. 更新了相关的消息处理逻辑

## 主要修改内容

### 1. assistantWelcomeId 获取逻辑修正

#### 修改前：
```javascript
// 错误的假设：认为返回的是包含 assistantWelcomeId 的对象
const responseData = res.data.data || {}
if (responseData.assistantWelcomeId) {
  this.setData({
    assistantWelcomeId: responseData.assistantWelcomeId
  })
}
return responseData.messages || []
```

#### 修改后：
```javascript
// 正确的处理：res.data.data 直接就是 assistantWelcomeId
const assistantWelcomeId = res.data.data

if (assistantWelcomeId) {
  this.setData({
    assistantWelcomeId: assistantWelcomeId
  })
  console.log('保存 assistantWelcomeId:', assistantWelcomeId)
}

// 由于接口只返回 assistantWelcomeId，不返回消息内容，返回空数组使用默认消息
return []
```

### 2. 新增消息类型模板渲染

在 `wxapp/pages_videoConsult/consultRoom/template/consult.wxml` 中添加了两个新的消息类型：

#### type=10015：小助理问候语
```xml
<!-- 小助理问候语 -->
<view wx:if="{{item.type == 10015}}" class="f28 c333 m30 p20 flex_c"
    style='background: #EAEAEA;border-radius: 8rpx;'>
    {{item.content}}
</view>
```

#### type=10016：您发起了视频问诊
```xml
<!-- 您发起了视频问诊 -->
<view wx:if="{{item.type == 10016}}" class="f28 c333 m30 p20 flex_c"
    style='background: #EAEAEA;border-radius: 8rpx;'>
    {{item.content}}
</view>
```

### 3. 消息显示条件更新

更新了医生发送消息的显示条件，添加了新的消息类型：

#### 修改前：
```xml
wx:if="{{item.relation == 1 && (item.type == 1 || item.type == 2 || item.type == 4 || item.type == 16 || item.type == 17 || item.type == 10005 ||item.type == 10013)}}"
```

#### 修改后：
```xml
wx:if="{{item.relation == 1 && (item.type == 1 || item.type == 2 || item.type == 4 || item.type == 16 || item.type == 17 || item.type == 10005 ||item.type == 10013 || item.type == 10015 || item.type == 10016)}}"
```

### 4. 消息 relation 字段设置

在历史消息和实时消息处理中，都添加了新消息类型的 relation 设置：

#### 历史消息处理：
```javascript
// 对于特殊类型的消息（如病历、处方、助理消息），通常是医生发送的
if ([10005, 16, 17, 10006, 10007, 10008, 10009, 10010, 10011, 10012, 10013, 10015, 10016].includes(item.type)) {
  item.relation = 1 // 医生发送
} else {
  item.relation = 0 // 默认为患者发送
}
```

#### 实时消息处理：
```javascript
// 对于特殊类型的消息（如病历、处方、助理消息），通常是医生发送的
if ([10005, 16, 17, 10006, 10007, 10008, 10009, 10010, 10011, 10012, 10013, 10015, 10016].includes(message.type)) {
  message.relation = 1 // 医生发送
} else {
  message.relation = 0 // 默认为患者发送
}
```

## 消息类型说明

### type=10015：小助理问候语
- **用途**：显示小助理的问候消息
- **样式**：灰色背景的系统消息样式
- **发送者**：设置为医生发送（relation=1）
- **内容**：通过 `item.content` 字段显示

### type=10016：您发起了视频问诊
- **用途**：显示用户发起视频问诊的系统提示
- **样式**：灰色背景的系统消息样式
- **发送者**：设置为医生发送（relation=1）
- **内容**：通过 `item.content` 字段显示

## 接口响应格式

### 初始消息接口
- **接口地址**：`GET /user/ap/patient/consult/video/initial/messages`
- **响应格式**：
  ```json
  {
    "code": 0,
    "msg": "success",
    "data": "welcome_12345"  // 直接返回 assistantWelcomeId 字符串
  }
  ```

### videoPayInfo 接口
- **新增参数**：`assistantWelcomeId`
- **参数示例**：
  ```json
  {
    "inquirerId": "12345",
    "price": 0,
    "conditionDesc": "无",
    "offlineDiagnosis": "无",
    "subOrderCode": "SUB123",
    "packageCode": "PKG456",
    "assistantWelcomeId": "welcome_12345"
  }
  ```

## 数据流程

### 1. 页面初始化流程
```
用户进入页面 
→ 调用初始消息接口 
→ 获取 assistantWelcomeId 
→ 保存到页面数据 
→ 显示默认助理消息
```

### 2. 消息显示流程
```
接收到 type=10015/10016 消息 
→ 设置 relation=1（医生发送） 
→ 在模板中匹配对应的渲染条件 
→ 显示为系统消息样式
```

### 3. 发起视频咨询流程
```
用户点击呼叫医生 
→ 调用 initiateVideoConsultation 
→ 将 assistantWelcomeId 添加到请求参数 
→ 调用 videoPayInfo 接口 
→ 后端可以关联助理消息和视频咨询
```

## 样式说明

新增的两个消息类型使用与其他系统消息相同的样式：
- **背景色**：`#EAEAEA`
- **边框圆角**：`8rpx`
- **字体大小**：`f28`（28rpx）
- **字体颜色**：`c333`（#333333）
- **布局**：居中显示（`flex_c`）
- **内外边距**：`m30 p20`

## 测试要点

### 1. assistantWelcomeId 获取测试
```javascript
// 在控制台检查
const pages = getCurrentPages()
const currentPage = pages[pages.length - 1]
console.log('assistantWelcomeId:', currentPage.data.assistantWelcomeId)
```

### 2. 新消息类型显示测试
- 模拟接收 type=10015 的消息，验证是否正确显示
- 模拟接收 type=10016 的消息，验证是否正确显示
- 验证消息样式是否正确

### 3. 参数传递测试
- 发起视频咨询时，验证 `assistantWelcomeId` 是否正确传递给后端

## 调试信息

在控制台中可以看到以下关键日志：

```javascript
// 获取 assistantWelcomeId
"初始消息接口调用成功: welcome_12345"
"保存 assistantWelcomeId: welcome_12345"

// 消息处理
"处理视频问诊实时消息: {type: 10015, content: '...', relation: 1}"
"处理视频问诊实时消息: {type: 10016, content: '...', relation: 1}"

// 发起视频咨询
"添加 assistantWelcomeId 参数: welcome_12345"
```

## 相关文件

- `wxapp/pages_videoConsult/consultRoom/index.js` - 主要逻辑修改
- `wxapp/pages_videoConsult/consultRoom/template/consult.wxml` - 模板渲染修改
- `wxapp/config/api.js` - 接口定义

## 注意事项

1. **消息类型扩展**：如果后续需要添加更多消息类型，需要同时更新：
   - 模板中的渲染条件
   - 消息显示条件
   - relation 字段设置逻辑

2. **样式一致性**：新增的消息类型使用了与其他系统消息相同的样式，保持界面一致性

3. **向后兼容**：所有修改都保持向后兼容，不影响现有功能

4. **错误处理**：如果 `assistantWelcomeId` 获取失败，不影响正常的视频咨询流程
