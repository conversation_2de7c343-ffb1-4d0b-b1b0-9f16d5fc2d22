const app = getApp()
const api = require('../../config/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    ...app.globalData,
    messageArr: [],
    scrollIntoView: '', //滚动条位置
    previewList: [], //预览图片
    doctorInfo: null, //医生信息
    chatkey: null, //聊天记录缓存key
    patientId: null, //患者id
    sessionId: null,
    baseUrl: '',
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '视频咨询室',
    statusBarHeight: null,
    scrollHeight: '70vh',
    scrollTop: 160,
    // 聊天历史数据相关
    historyPage: 1, //页码
    hasPrev: true, //是否有上一页
    static: {
      nomes: api.ImgUrl + 'images/nomes.png',
      bg_follow: api.ImgUrl + 'images/bg_follow.png',
      bg_medical_record: api.ImgUrl + 'images/bg_medical_record.png',
      bg_prescription: api.ImgUrl + 'images/bg_prescription.png',
      ic_follow_visit_01: api.ImgUrl + 'images/ic_follow_visit_01.png',
      ic_follow_questionnaire_02: api.ImgUrl + 'images/ic_follow_questionnaire_02.png'
    },
    ic_service_record: api.ImgUrl + 'images/video_consult/<EMAIL>', // 服务记录icon
    ic_patient_edit: api.ImgUrl + 'images/video_consult/<EMAIL>', // 编辑就诊人icon
    img_customer_service_head: api.ImgUrl + 'images/video_consult/<EMAIL>', // 小诺助理头像
    avatar: null, //用户头像
    consultId: null, //问诊ID
    consultType: 2, //问诊类型标识：2为视频问诊
    subOrderCode: null, //子订单编码
    packageCode: null, //套餐编码

    // 就诊人相关数据
    patientList: [], //就诊人列表
    currentPatientIndex: 0, //当前选中的就诊人索引
    currentPatient: null, //当前选中的就诊人信息
    patientScrollLeft: 0, //就诊人滚动位置

    // 页面控制
    showPatientInfo: true, //是否显示就诊人信息区域
    fromHistoryList: false, //是否来自历史列表页面

    // 初始化消息
    hasInitialMessages: false, //是否已添加初始消息
    assistantWelcomeId: null, //助理欢迎消息ID，用于发起视频咨询时传递给后端
    isAgreement: false,
    showPopup: false,
    checked: false, //是否已勾选互联网问诊知情同意书
    needRefreshPatientList: false //是否需要刷新就诊人列表
  },

  //消息到达更新，执行util中onMessageArrived方法，不可删除
  onMessageArrived() {
    this.updateResult(arguments[0], () => {
      this.setData({
        scrollIntoView: 'chat_' + arguments[0].sendTime
      })
    }, arguments[1])
  },

  onLoad(options) {
    console.log('视频咨询室页面加载', options)
    this.ageePopup = this.selectComponent('#ageePopup') // 获取协议弹框组件

    // 判断是否来自历史列表页面（pages/consult/index/index）
    const fromHistoryList = options.from === 'history' || options.roomID
    // 判断是否为待接诊状态
    const isWaitingStatus = options.status === 'waiting'
    // 待接诊状态或来自历史列表则隐藏就诊人信息区域
    const showPatientInfo = !fromHistoryList && !isWaitingStatus

    this.setData({
      avatar: app.globalData.userInfo.avatar, //用户头像
      patientId: app.globalData.userInfo.userId,
      statusBarHeight: app.globalData.statusBarHeight,
      consultId: options.consultId || options.videoConsultId || options.id,
      doctorId: options.doctorId,
      subOrderCode: options.subOrderCode || null, //子订单编码
      packageCode: options.packageCode || null, //套餐编码
      fromHistoryList: fromHistoryList,
      isWaitingStatus: isWaitingStatus, //是否为待接诊状态
      showPatientInfo: showPatientInfo
    })
    console.log('视频咨询参数:', {
      consultId: this.data.consultId,
      doctorId: this.data.doctorId,
      subOrderCode: this.data.subOrderCode,
      packageCode: this.data.packageCode,
      fromHistoryList: fromHistoryList,
      isWaitingStatus: isWaitingStatus,
      showPatientInfo: showPatientInfo
    })
    this.init(options)
  },

  async onShow() {
    console.log('视频咨询室页面 onShow 触发')

    // 检查是否需要刷新就诊人列表
    this.checkAndRefreshPatientList()

    // 初始化协议弹框
    if (this.ageePopup) {
      await this.ageePopup.getArticle()
    }

    // 只有在页面首次加载或者聊天数据为空时才重新初始化
    // 避免从病历详情等页面返回时重复加载数据
    if (this.data.consultId && this.data.doctorId) {
      const chatkey = 'video_' + this.data.patientId + '_' + this.data.doctorId + '_' + this.data.consultId

      // 检查是否需要重新初始化聊天数据
      const needReinitChat = !this.data.chatkey ||
                            this.data.chatkey !== chatkey ||
                            !this.data.messageArr ||
                            this.data.messageArr.length === 0

      console.log('检查是否需要重新初始化聊天数据:', {
        needReinitChat,
        currentChatkey: this.data.chatkey,
        newChatkey: chatkey,
        messageArrLength: this.data.messageArr ? this.data.messageArr.length : 0
      })

      if (needReinitChat) {
        console.log('需要重新初始化聊天数据')
        this.setData({
          chatkey: chatkey
        }, () => {
          this.initChatData()
        })
      } else {
        console.log('聊天数据已存在，无需重新初始化')
      }
    }
  },

  onHide() {
    console.log('视频咨询室页面 onHide 触发')
  },

  onUnload() {
    console.log('视频咨询室页面 onUnload 触发')
    // 页面卸载时清理状态
    this.resetPageState()
  },

  /**
   * 重置页面状态
   * 在页面卸载或需要重新初始化时调用
   */
  resetPageState() {
    console.log('重置视频咨询室页面状态')
    this.data.historyPage = 1
    this.data.hasPrev = true
    this.loading = false
  },

  /**
   * 检查并刷新就诊人列表
   * 使用全局标识来判断是否需要刷新数据
   */
  checkAndRefreshPatientList() {
    // 检查全局标识，判断是否需要刷新就诊人列表
    const app = getApp()
    if (app.globalData && app.globalData.needRefreshVideoConsultPatientList) {
      console.log('检测到需要刷新就诊人列表，开始刷新...')

      // 清除全局标识
      app.globalData.needRefreshVideoConsultPatientList = false

      // 只有在显示就诊人信息区域时才刷新就诊人列表
      if (this.data.showPatientInfo) {
        this.getPatientList()
      }
    }
  },

  /**
   * 初始化
   */
  init(options) {
    if (options.doctorId) {
      this.getDoctorDetail()
    }

    // 只有在显示就诊人信息区域时才获取就诊人列表
    if (this.data.showPatientInfo) {
      this.getPatientList()
    } else {
      // 如果不显示就诊人信息，直接初始化聊天数据
      this.setData({
        hasInitialMessages: true // 来自历史列表时不添加初始消息
      })
    }

    this.initScrollHeigth()
  },

  /**
   * 获取医生详情
   */
  async getDoctorDetail() {
    try {
      const res = await util.request(api.doctorDetail, {
        doctorId: this.data.doctorId,
        patientId: this.data.patientId
      }, 'POST', '2')
      if (res.data.code === 0) {
        this.setData({
          doctorInfo: res.data.data
        }, () => {
          // 获取医生信息后，更新滚动高度以适应医生信息区域的显示
          console.log('医生信息获取成功，更新滚动高度')
          this.updateScrollHeight({
            forceUpdate: true
          })
        })
      } else {
        console.error('获取医生详情失败', res.data.msg)
      }
    } catch (err) {
      console.error('获取医生详情失败', err)
    }
  },

  /**
   * 获取就诊人列表
   */
  async getPatientList() {
    try {
      // 显示加载状态
      wx.showLoading({
        title: '加载就诊人信息...',
        mask: true
      })

      // 获取就诊人列表入参
      const params = {
        packageCode: this.data.packageCode, // 套餐编码
        subOrderCode: this.data.subOrderCode, // 子订单号
        patientId: this.data.patientId // 患者ID
      }

      console.log('获取就诊人列表参数:', params)

      const res = await util.request(api.getVideoInquirerRelationList, params, 'get', 1)

      wx.hideLoading()

      if (res.data.code === 0) {
        const patientList = res.data.data || []

        // 输出接口返回的原始数据结构，便于调试
        console.log('接口返回的原始就诊人数据:', patientList)

        // 处理数据格式，确保与现有结构兼容
        const formattedList = patientList.map((item, index) => {
          // 根据接口返回的字段进行映射
          const formattedItem = {
            inquirerId: item.inquirerId, // 就诊人ID，用于编辑跳转
            name: item.name || item.patientName || item.contactName || '未知', // 就诊人姓名
            age: item.age || '', // 年龄
            gender: item.gender || '', // 性别 (0: 女, 1: 男)
            relationName: item.relationName || item.relation || '', // 关系名称
            childTag: item.childTag || 0, // 儿童标识
            holderId: item.holderId || item.customerId || '', // 客户号，用于视频咨询参数传递

            // 判断信息是否完整 - 根据实际字段判断
            // 姓名、性别、年龄、关系都有值才认为信息完整
            isInfoComplete: !!(
              (item.name || item.contactName) && // 有姓名
              (item.gender !== undefined && item.gender !== null && item.gender !== '') && // 有性别
              (item.age && item.age !== '') && // 有年龄
              (item.relationName || item.relation) // 有关系
            ),

            // 保留原始数据，便于后续使用
            originalData: item,

            // 其他可能需要的字段
            phone: item.phone || item.contactPhone || '',
            idCard: item.idCard || item.guardianIdCard || '',
            address: item.address || '',
            birthday: item.birthday || '',
            maritalStatus: item.maritalStatus || 0,
            myself: item.myself || false
          }

          // 输出每个格式化后的就诊人数据
          console.log(`格式化后的就诊人数据 [${index}]:`, {
            inquirerId: formattedItem.inquirerId,
            name: formattedItem.name,
            age: formattedItem.age,
            gender: formattedItem.gender,
            relationName: formattedItem.relationName,
            isInfoComplete: formattedItem.isInfoComplete,
            infoStatus: formattedItem.isInfoComplete ? '信息完整' : '信息待补充'
          })

          return formattedItem
        })

        console.log('获取就诊人列表成功，格式化后的完整数据:', formattedList)

        this.setData({
          patientList: formattedList,
          currentPatient: formattedList[0] || null
        }, () => {
          // 添加初始消息
          this.addInitialMessages()

          // 就诊人列表获取成功后，更新滚动高度以适应就诊人信息区域的显示
          console.log('就诊人列表获取成功，更新滚动高度')
          this.updateScrollHeight({
            forceUpdate: true
          })
        })

        if (formattedList.length === 0) {
          wx.showToast({
            title: '暂无可用的就诊人',
            icon: 'none',
            duration: 2000
          })
        }
      } else {
        console.error('获取就诊人列表失败', res.data.msg)
        wx.showToast({
          title: res.data.msg || '获取就诊人信息失败',
          icon: 'none',
          duration: 2000
        })

        // 接口失败时使用空列表
        this.setData({
          patientList: [],
          currentPatient: null
        })
      }

    } catch (err) {
      console.error('获取就诊人列表失败', err)
      wx.hideLoading()
      wx.showToast({
        title: '网络异常，请稍后重试',
        icon: 'none',
        duration: 2000
      })

      // 网络异常时使用空列表
      this.setData({
        patientList: [],
        currentPatient: null
      })
    }
  },

  /**
   * 初始化聊天数据
   * 视频问诊不使用本地缓存，直接从历史记录接口获取所有消息
   */
  initChatData() {
    console.log('视频问诊初始化聊天数据 - 不使用本地缓存，直接获取历史数据')

    // 重置分页状态，避免从其他页面返回时页码错误
    this.data.hasPrev = true
    this.data.historyPage = 1
    this.loading = false

    this.setData({
      messageArr: [],
      previewList: [],
      historyPage: 1
    })

    console.log('重置分页状态，historyPage:', this.data.historyPage)

    // 直接获取历史数据，不依赖本地缓存
    // 传入 resetPagination=true 表示重置分页状态
    this.getHistoryData(false, true)
  },

  /**
   * 获取聊天历史数据
   * 视频问诊始终获取历史聊天记录，不再区分进入来源
   * @param {boolean} isScroll - 是否为滚动加载更多
   * @param {boolean} resetPagination - 是否重置分页状态
   */
  getHistoryData(isScroll = false, resetPagination = false) {
    console.log('视频咨询加载历史数据...', {
      isScroll,
      resetPagination,
      currentHistoryPage: this.data.historyPage,
      fromHistoryList: this.data.fromHistoryList,
      consultId: this.data.consultId,
      doctorId: this.data.doctorId
    })

    // 如果需要重置分页状态（通常在重新初始化时）
    if (resetPagination) {
      console.log('重置分页状态')
      this.data.historyPage = 1
      this.data.hasPrev = true
      this.loading = false
    }

    if (!this.loading && this.data.hasPrev) {
      this.loading = true
      const params = {
        toId: this.data.doctorId,
        patientId: this.data.patientId,
        page: this.data.historyPage,
        num: 10,
        consultType: 2, // 视频咨询需要传2
        consultId: this.data.consultId // 视频咨询需要传consultId
      }
      console.log(this.data.messageArr[0], 'video history first message')
      if (this.data.messageArr[0] && this.data.messageArr[0].messages[0]) {
        params.beginTime = this.data.messageArr[0].messages[0].sendTime ? this.data.messageArr[0].messages[0].sendTime : ''
      }
      console.log(this.data.messageArr, 'video history messageArr')
      util.showToast({
        title: '加载中..',
        icon: 'loading'
      })
      util.request(api.chatHistory, params, 'POST', 2)
        .then(res => {
          console.log('=========视频咨询历史数据==============', res)
          var dcode = res.data
          if (dcode.code === 0) {
            const messageId = []
            var cdata = dcode.data.result.reverse()
            if (cdata.length > 0) {
              cdata.forEach(element => {
                element.messages = element.messages.reverse()
                element.timeText = util.calcTimeHeader(element.timeGroup)
                element.messages.forEach(item => {
                  messageId.push(item.id)

                  // 确保历史消息也有正确的relation字段
                  if (item.relation === undefined || item.relation === null) {
                    const currentPatientId = this.data.patientId
                    if (item.from && item.from.id === currentPatientId) {
                      item.relation = 0 // 患者发送
                    } else if (item.to && item.to.id === currentPatientId) {
                      item.relation = 1 // 医生发送
                    } else {
                      // 对于特殊类型的消息（如病历、处方），通常是医生发送的
                      if ([10005, 16, 17, 10006, 10007, 10008, 10009, 10010, 10011, 10012, 10013].includes(item.type)) {
                        item.relation = 1 // 医生发送
                      } else {
                        item.relation = 0 // 默认为患者发送
                      }
                    }
                  }

                  if (item.type === 2) {
                    this.data.previewList.push(dcode.data.baseUrl + item.content.path)
                  }
                })
              })
              // 防止历史消息与推送消息重复
              const flag = this.messageFilter(messageId)
              var message = flag ? this.data.messageArr : cdata.concat(this.data.messageArr)
              const _msgs = message[message.length - 1].messages
              let scrollIntoView = ''
              if (!isScroll) {
                scrollIntoView = 'chat_' + _msgs[_msgs.length - 1].sendTime
              } else {
                scrollIntoView = 'chat_' + message[cdata.length].messages[0].sendTime
              }
              this.setData({
                baseUrl: dcode.data.baseUrl,
                messageArr: message,
                previewList: this.data.previewList,
                scrollIntoView: scrollIntoView,
                historyPage: this.data.historyPage + 1
              }, () => {
                const platform = util.getPlatform()
                if (platform === 'ios') {
                  this.setData({
                    scrollIntoView
                  })
                } else {
                  if (!isScroll) {
                    this.setData({
                      scrollIntoView
                    })
                  }
                }
                setTimeout(() => {
                  this.loading = false
                }, 500)
                util.hideToast()
              })
              if (dcode.data.totalPage <= dcode.data.pageNo) {
                console.log('视频咨询没有下一页')
                this.setData({
                  hasPrev: false
                })
              }
            } else {
              console.log('视频咨询没有历史数据')
              this.loading = false
              util.hideToast()
            }
          } else {
            console.log('获取视频咨询历史数据失败', dcode.msg)
            this.loading = false
            wx.showToast({
              title: dcode.msg,
              icon: 'none'
            })
          }
        })
        .catch(() => {
          setTimeout(() => {
            util.hideToast()
            this.loading = false
          }, 500)
        })
    }
  },

  /**
   * 防止同时请求接口并推送消息内容重复
   */
  messageFilter(messageId) {
    let k = 0
    let isRep = false
    for (let i = 0; i < this.data.messageArr.length; i++) {
      const messages = this.data.messageArr[i].messages
      for (let j = 0; j < messages.length; j++) {
        if (k === 10) {
          break
        }
        k++
        if (messageId.indexOf(messages[j].id) !== -1) {
          isRep = true
          break
        }
      }
      if (k === 10) {
        break
      }
    }
    return isRep
  },

  /**
   * 滚动到顶部时加载更多历史数据
   * 视频问诊始终允许加载更多历史数据
   */
  loadMore() {
    console.log('视频咨询滚动到顶部，加载更多历史数据')
    this.getHistoryData(true)
  },

  /**
   * 更新消息结果 - 视频问诊实时消息处理
   * 不使用本地缓存，直接处理实时消息并添加到当前消息列表
   */
  updateResult(message, callback, _chatLength) {
    console.log('视频问诊收到实时消息:', message)

    // 只处理视频问诊相关的消息
    // consultType为2是视频问诊，且consultId要匹配当前问诊ID
    if (!message) {
      console.log('消息为空，忽略')
      return
    }

    if (message.consultType !== 2) {
      console.log('非视频问诊消息，忽略:', message.consultType)
      return
    }

    if (Number(message.consultId) !== Number(this.data.consultId)) {
      console.log('consultId不匹配，忽略:', {
        messageConsultId: message.consultId,
        currentConsultId: this.data.consultId
      })
      return
    }

    console.log('处理视频问诊实时消息:', {
      type: message.type,
      consultType: message.consultType,
      consultId: message.consultId,
      relation: message.relation,
      from: message.from,
      to: message.to,
      content: message.content
    })

    // 直接将消息添加到当前消息列表，不使用缓存
    this.addMessageToList(message, callback)
  },

  /**
   * 将消息添加到消息列表
   * @param {Object} message - 消息对象
   * @param {Function} callback - 回调函数
   */
  addMessageToList(message, callback) {
    // 确保消息有正确的relation字段
    // relation: 0=患者发送, 1=医生发送
    if (message.relation === undefined || message.relation === null) {
      // 根据from和to字段判断消息方向
      const currentPatientId = this.data.patientId
      if (message.from && message.from.id === currentPatientId) {
        message.relation = 0 // 患者发送
      } else if (message.to && message.to.id === currentPatientId) {
        message.relation = 1 // 医生发送
      } else {
        // 对于特殊类型的消息（如病历、处方），通常是医生发送的
        if ([10005, 16, 17, 10006, 10007, 10008, 10009, 10010, 10011, 10012, 10013].includes(message.type)) {
          message.relation = 1 // 医生发送
        } else {
          message.relation = 0 // 默认为患者发送
        }
      }
    }

    console.log('添加消息到列表，relation已设置:', {
      type: message.type,
      relation: message.relation,
      from: message.from,
      to: message.to
    })

    const currentTime = message.sendTime || Date.now()
    const messageArr = [...this.data.messageArr]

    // 检查是否需要创建新的时间分组（2分钟内的消息归为一组）
    let needNewGroup = true
    if (messageArr.length > 0) {
      const lastGroup = messageArr[messageArr.length - 1]
      const timeDiff = currentTime - lastGroup.sendTime
      if (timeDiff <= 120000) { // 2分钟内
        needNewGroup = false
        lastGroup.messages.unshift(message)
        lastGroup.sendTime = currentTime
      }
    }

    if (needNewGroup) {
      messageArr.push({
        messages: [message],
        timeGroup: currentTime,
        sendTime: currentTime,
        timeText: util.calcTimeHeader(currentTime)
      })
    }

    // 更新预览图片列表
    const previewList = [...this.data.previewList]
    if (message.type === 2 && message.content && message.content.path) {
      const imagePath = this.data.baseUrl
        ? this.data.baseUrl + message.content.path
        : message.content.path
      previewList.push(imagePath)
    }

    this.setData({
      messageArr: messageArr,
      previewList: previewList
    }, () => {
      console.log('视频问诊实时消息已添加到列表')
      if (callback) {
        callback()
      }
    })
  },

  /**
   * 初始化滚动高度
   * 动态计算聊天消息区域的滚动高度，考虑就诊人信息区域和医生信息区域的可见性
   */
  initScrollHeigth() {
    console.log('开始计算滚动高度，当前状态:', {
      showPatientInfo: this.data.showPatientInfo,
      fromHistoryList: this.data.fromHistoryList,
      isWaitingStatus: this.data.isWaitingStatus,
      hasDoctorInfo: !!this.data.doctorInfo?.name
    })

    const query = wx.createSelectorQuery().in(this)
    const elementsToQuery = []

    // 根据实际显示状态查询相应的UI元素
    if (this.data.showPatientInfo) {
      // 就诊人信息区域显示时，查询其高度
      query.select('#patientBox').boundingClientRect()
      elementsToQuery.push('patientBox')
      console.log('添加就诊人信息区域到查询列表')
    }

    // 检查医生信息区域是否显示（有医生信息时才显示）
    if (this.data.doctorInfo && this.data.doctorInfo.name) {
      query.select('#dobBox').boundingClientRect()
      elementsToQuery.push('dobBox')
      console.log('添加医生信息区域到查询列表')
    }

    // 如果没有需要查询的元素，直接使用默认计算
    if (elementsToQuery.length === 0) {
      console.log('没有UI区域需要查询，使用默认高度计算')
      this.calculateScrollHeightWithDefaults()
      return
    }

    query.exec((res) => {
      console.log('UI元素高度查询结果:', res)

      let patientBoxHeight = 0
      let dobBoxHeight = 0
      let resIndex = 0

      // 根据查询顺序解析结果
      if (this.data.showPatientInfo && elementsToQuery.includes('patientBox')) {
        const patientBoxResult = res[resIndex]
        patientBoxHeight = patientBoxResult ? patientBoxResult.height : this.getDefaultPatientBoxHeight()
        console.log('就诊人信息区域高度:', patientBoxHeight, 'px')
        resIndex++
      }

      if (this.data.doctorInfo?.name && elementsToQuery.includes('dobBox')) {
        const dobBoxResult = res[resIndex]
        dobBoxHeight = dobBoxResult ? dobBoxResult.height : this.getDefaultDoctorBoxHeight()
        console.log('医生信息区域高度:', dobBoxHeight, 'px')
        resIndex++
      }

      // 计算总的占用高度
      const navigationHeight = this.data.statusBarHeight + 44 // 导航栏高度
      const totalOccupiedHeight = patientBoxHeight + dobBoxHeight + navigationHeight

      console.log('高度计算详情:', {
        patientBoxHeight,
        dobBoxHeight,
        navigationHeight,
        totalOccupiedHeight
      })

      // 设置滚动区域高度和位置
      this.setData({
        scrollHeight: `calc(100vh - ${totalOccupiedHeight}px - 30rpx)`,
        scrollTop: totalOccupiedHeight
      })

      console.log('滚动高度设置完成:', {
        scrollHeight: `calc(100vh - ${totalOccupiedHeight}px - 30rpx)`,
        scrollTop: totalOccupiedHeight
      })
    })
  },

  /**
   * 使用默认值计算滚动高度（当无法查询到实际UI元素时）
   */
  calculateScrollHeightWithDefaults() {
    const navigationHeight = this.data.statusBarHeight + 44
    const patientBoxHeight = this.data.showPatientInfo ? this.getDefaultPatientBoxHeight() : 0
    const dobBoxHeight = this.data.doctorInfo?.name ? this.getDefaultDoctorBoxHeight() : 0
    const totalOccupiedHeight = patientBoxHeight + dobBoxHeight + navigationHeight

    console.log('使用默认高度计算:', {
      patientBoxHeight,
      dobBoxHeight,
      navigationHeight,
      totalOccupiedHeight
    })

    this.setData({
      scrollHeight: `calc(100vh - ${totalOccupiedHeight}px - 30rpx)`,
      scrollTop: totalOccupiedHeight
    })
  },

  /**
   * 获取就诊人信息区域的默认高度
   */
  getDefaultPatientBoxHeight() {
    // 根据UI设计计算：padding(30rpx*2) + 标题行(约40rpx) + 滑动区域(120rpx) + 复选框(约50rpx) + 按钮(88rpx) + 间距
    // 转换为px：(30*2 + 40 + 120 + 50 + 88 + 30) * (屏幕宽度/750)
    const systemInfo = wx.getSystemInfoSync()
    const rpxToPx = systemInfo.windowWidth / 750
    return Math.ceil(358 * rpxToPx) // 约358rpx转换为px
  },

  /**
   * 获取医生信息区域的默认高度
   */
  getDefaultDoctorBoxHeight() {
    // 根据UI设计计算：padding(10px*2) + 医生头像和信息(42px) + 可能的按钮高度
    const baseHeight = 20 + 42 // padding + 头像区域
    const buttonHeight = this.data.isWaitingStatus ? 44 : 0 // 待接诊状态下有按钮
    return baseHeight + buttonHeight
  },

  /**
   * 动态更新滚动高度
   * 当UI区域的可见性发生变化时调用此方法重新计算滚动高度
   * @param {Object} options - 更新选项
   * @param {boolean} options.showPatientInfo - 是否显示就诊人信息区域
   * @param {boolean} options.hasDoctorInfo - 是否有医生信息
   * @param {boolean} options.forceUpdate - 是否强制更新（即使状态没有变化）
   */
  updateScrollHeight(options = {}) {
    const {
      showPatientInfo = this.data.showPatientInfo,
      hasDoctorInfo = !!(this.data.doctorInfo && this.data.doctorInfo.name),
      forceUpdate = false
    } = options

    console.log('动态更新滚动高度:', {
      currentShowPatientInfo: this.data.showPatientInfo,
      newShowPatientInfo: showPatientInfo,
      currentHasDoctorInfo: !!(this.data.doctorInfo && this.data.doctorInfo.name),
      newHasDoctorInfo: hasDoctorInfo,
      forceUpdate
    })

    // 检查是否需要更新
    const needUpdate = forceUpdate ||
                      showPatientInfo !== this.data.showPatientInfo ||
                      hasDoctorInfo !== !!(this.data.doctorInfo && this.data.doctorInfo.name)

    if (!needUpdate) {
      console.log('滚动高度无需更新')
      return
    }

    // 更新状态（如果有变化）
    if (showPatientInfo !== this.data.showPatientInfo) {
      this.setData({
        showPatientInfo: showPatientInfo
      })
    }

    // 延迟执行以确保DOM更新完成
    setTimeout(() => {
      this.initScrollHeigth()
    }, 100)
  },

  /**
   * 切换就诊人信息区域的显示状态
   * @param {boolean} show - 是否显示就诊人信息区域
   */
  togglePatientInfoDisplay(show) {
    console.log('切换就诊人信息区域显示状态:', show)

    this.updateScrollHeight({
      showPatientInfo: show,
      forceUpdate: true
    })
  },

  /**
   * 测试方法：手动触发滚动高度重新计算
   * 可以在开发调试时使用，验证滚动高度计算是否正确
   */
  testScrollHeightCalculation() {
    console.log('=== 开始测试滚动高度计算 ===')
    console.log('当前页面状态:', {
      showPatientInfo: this.data.showPatientInfo,
      fromHistoryList: this.data.fromHistoryList,
      isWaitingStatus: this.data.isWaitingStatus,
      hasDoctorInfo: !!(this.data.doctorInfo && this.data.doctorInfo.name),
      currentScrollHeight: this.data.scrollHeight,
      currentScrollTop: this.data.scrollTop
    })

    // 强制重新计算滚动高度
    this.updateScrollHeight({
      forceUpdate: true
    })

    console.log('=== 滚动高度计算测试完成 ===')
  },

  /**
   * 图片预览
   */
  previewImage(e) {
    const src = e.currentTarget.dataset.src
    wx.previewImage({
      current: src,
      urls: this.data.previewList
    })
  },

  /**
   * 添加初始消息
   * 从后端接口获取助理消息，而不是使用硬编码的消息
   */
  async addInitialMessages() {
    if (this.data.hasInitialMessages) {
      return
    }

    console.log('开始获取视频问诊初始助理消息')

    try {
      // 调用后端接口获取初始助理消息
      const initialMessages = await this.getInitialMessagesFromAPI()

      if (initialMessages && initialMessages.length > 0) {
        console.log('从后端获取到初始消息:', initialMessages)

        // 将后端消息转换为前端消息格式并添加到消息列表
        this.processAndAddInitialMessages(initialMessages)
      } else {
        console.log('后端未返回初始消息，使用默认消息')
        // 如果后端没有返回消息，使用默认的助理消息
        this.addDefaultInitialMessages()
      }

    } catch (err) {
      console.error('获取初始助理消息失败:', err)
      // 接口调用失败时，使用默认的助理消息
      this.addDefaultInitialMessages()
    }
  },

  /**
   * 从后端API获取初始助理消息
   * @returns {Promise<Array>} 初始消息数组
   */
  async getInitialMessagesFromAPI() {
    try {
      // 构建请求参数
      const params = {
        patientId: this.data.patientId,
        packageCode: this.data.packageCode,
        subOrderCode: this.data.subOrderCode
      }

      console.log('调用初始消息接口，参数:', params)

      // 调用后端接口
      const res = await util.request(api.getVideoConsultInitialMessages, params, 'post', '2')

      if (res.data.code === 0) {
        console.log('初始消息接口调用成功:', res.data.data)

        const responseData = res.data.data || {}

        // 保存 assistantWelcomeId 到页面数据中，供后续发起视频咨询时使用
        if (responseData.assistantWelcomeId) {
          this.setData({
            assistantWelcomeId: responseData.assistantWelcomeId
          })
          console.log('保存 assistantWelcomeId:', responseData.assistantWelcomeId)
        }

        // 返回消息数组
        return responseData.messages || []
      } else {
        console.error('初始消息接口返回错误:', res.data.msg)
        return []
      }

    } catch (err) {
      console.error('初始消息接口调用异常:', err)
      throw err
    }
  },

  /**
   * 处理并添加从后端获取的初始消息
   * @param {Array} backendMessages 后端返回的消息数组
   */
  processAndAddInitialMessages(backendMessages) {
    const currentTime = new Date().getTime()
    const messageArr = []

    backendMessages.forEach((backendMsg, index) => {
      // 将后端消息格式转换为前端消息格式
      const message = {
        id: backendMsg.id || `init_${index + 1}_${currentTime}`,
        sendTime: backendMsg.sendTime || (currentTime + index * 1000),
        from: {
          id: backendMsg.fromId || 'assistant',
          name: backendMsg.fromName || '小诺助理'
        },
        to: {
          id: this.data.patientId
        },
        type: backendMsg.type || 1, // 默认为文本消息
        content: backendMsg.content || backendMsg.text || '',
        consultType: 2,
        consultId: this.data.consultId,
        relation: 1 // 助理消息，设为医生发送
      }

      // 添加到消息数组
      messageArr.push({
        messages: [message],
        timeGroup: message.sendTime,
        sendTime: message.sendTime,
        timeText: util.calcTimeHeader(message.sendTime)
      })
    })

    this.setData({
      messageArr: messageArr,
      hasInitialMessages: true
    })

    console.log('后端初始消息处理完成，共', backendMessages.length, '条消息')
  },

  /**
   * 添加默认的初始消息（当后端接口失败时使用）
   */
  addDefaultInitialMessages() {
    console.log('使用默认初始消息')

    const currentTime = new Date().getTime()
    const messages = [
      {
        id: 'init_1_' + currentTime,
        sendTime: currentTime,
        from: { id: 'assistant', name: '小诺助理' },
        to: { id: this.data.patientId },
        type: 1, // 文本消息
        content: '我是您的招商信诺助理-小诺\n健康问题请点击蓝色按钮【呼叫\n视频医生】',
        consultType: 2,
        consultId: this.data.consultId,
        relation: 1 // 助理消息，设为医生发送
      },
      {
        id: 'init_2_' + (currentTime + 1000),
        sendTime: currentTime + 1000,
        from: { id: 'assistant', name: '小诺助理' },
        to: { id: this.data.patientId },
        type: 1, // 文本消息
        content: '恭喜您！会员服务开通成功',
        consultType: 2,
        consultId: this.data.consultId,
        relation: 1 // 助理消息，设为医生发送
      }
    ]

    // 直接添加到消息列表，不使用缓存
    const messageArr = []
    messages.forEach((message) => {
      messageArr.push({
        messages: [message],
        timeGroup: message.sendTime,
        sendTime: message.sendTime,
        timeText: util.calcTimeHeader(message.sendTime)
      })
    })

    this.setData({
      messageArr: messageArr,
      hasInitialMessages: true
    })

    console.log('默认初始消息添加完成')
  },

  /**
   * 选择就诊人
   */
  selectPatient(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      currentPatientIndex: index,
      currentPatient: this.data.patientList[index] || null
    })

    // 自动滑动到选中的就诊人位置
    this.scrollToPatient(index)
  },

  /**
   * 滑动到指定就诊人位置，使其居中显示
   */
  scrollToPatient(index) {
    // 使用简单的计算方式：每个item宽度 + 间距
    const itemWidth = 282 // rpx 转换为 px 大约是 itemWidth + margin
    const itemMargin = 20
    const totalItemWidth = itemWidth + itemMargin

    // 获取scroll-view宽度
    const query = wx.createSelectorQuery().in(this)
    query.select('.patient-scroll').boundingClientRect()

    query.exec((res) => {
      if (res[0]) {
        const scrollViewWidth = res[0].width
        const scrollViewWidthRpx = scrollViewWidth * 750 / wx.getSystemInfoSync().windowWidth

        // 计算目标位置：让选中的item居中
        const targetScrollLeft = index * totalItemWidth - (scrollViewWidthRpx - itemWidth) / 2

        // 计算内容总宽度
        const totalContentWidth = this.data.patientList.length * totalItemWidth
        const maxScrollLeft = totalContentWidth - scrollViewWidthRpx

        // 确保滚动位置在有效范围内
        const finalScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft))

        this.setData({
          patientScrollLeft: finalScrollLeft
        })
      }
    })
  },

  /**
   * 编辑就诊人
   * 根据患者是否有 inquirerId 来决定跳转到编辑页面还是新增页面
   */
  editPatient(e) {
    const index = e.currentTarget.dataset.index
    const patient = this.data.patientList[index]

    console.log('编辑就诊人 - 点击索引:', index)
    console.log('编辑就诊人 - 选中的就诊人数据:', patient)

    if (patient) {
      let url = ''

      // 检查患者是否有 inquirerId
      if (patient.inquirerId) {
        // 有 inquirerId，跳转到编辑页面
        console.log('患者有 inquirerId，跳转到编辑页面')
        url = `/pages/peopleContent/detail/detail?inquirerId=${patient.inquirerId}&model=${patient.childTag || 0}`

        // 为编辑页面添加 inquirerId 参数
        console.log('编辑就诊人 - inquirerId:', patient.inquirerId)
      } else {
        // 没有 inquirerId，跳转到新增页面，并自动填充患者姓名
        console.log('患者没有 inquirerId，跳转到新增页面并自动填充姓名')
        url = `/pages/peopleContent/addPeople/addPeople?type=1`

        // 自动填充患者姓名到新增页面
        if (patient.name && patient.name !== '未知') {
          url += `&name=${encodeURIComponent(patient.name)}`
          console.log('编辑就诊人 - 自动填充姓名:', patient.name)
        }

        // 自动填充患者手机号到新增页面（用于收货人信息自动填充）
        if (patient.phone && patient.phone.trim() !== '') {
          url += `&phone=${encodeURIComponent(patient.phone)}`
          console.log('编辑就诊人 - 自动填充手机号:', patient.phone)
        }
      }

      // 添加 holderId 参数（客户号）- 两种页面都需要
      if (patient.holderId) {
        url += `&holderId=${patient.holderId}`
        console.log('编辑就诊人 - holderId:', patient.holderId)
      } else {
        console.warn('编辑就诊人 - holderId 字段缺失，将跳过该参数', patient)
      }

      // 添加视频咨询相关参数 - 两种页面都需要
      if (this.data.subOrderCode) {
        url += `&subOrderCode=${this.data.subOrderCode}`
      }
      if (this.data.packageCode) {
        url += `&packageCode=${this.data.packageCode}`
      }

      console.log('编辑就诊人 - 跳转URL:', url)
      console.log('编辑就诊人 - 关键参数验证:', {
        inquirerId: patient.inquirerId,
        holderId: patient.holderId,
        name: patient.name,
        childTag: patient.childTag,
        hasSubOrderCode: !!this.data.subOrderCode,
        hasPackageCode: !!this.data.packageCode,
        hasHolderId: !!patient.holderId,
        targetPage: patient.inquirerId ? '编辑页面' : '新增页面'
      })

      wx.navigateTo({
        url: url
      })
    } else {
      console.error('编辑就诊人失败: 未找到对应的就诊人数据', { index, patientListLength: this.data.patientList.length })
      wx.showToast({
        title: '未找到就诊人信息',
        icon: 'none'
      })
    }
  },

  /**
   * 呼叫视频医生
   */
  async callVideoDoctor() {
    // 如果是待接诊状态，直接跳转到呼叫医生页面
    if (this.data.isWaitingStatus) {
      this.handleWaitingStatusCall()
      return
    }

    if (!this.data.currentPatient) {
      wx.showToast({
        title: '请先选择就诊人',
        icon: 'none'
      })
      return
    }

    // 先校验就诊人信息
    const validateResult = await this.validatePatientInfo()
    if (!validateResult.success) {
      // 如果已经显示了弹框（如错误码 730012），则不再显示 toast
      if (!validateResult.showModal) {
        wx.showToast({
          title: validateResult.message,
          icon: 'none',
          duration: 3000
        })
      }
      return
    }

    // 检查是否已勾选互联网问诊知情同意书
    if (!this.data.checked) {
      wx.showToast({
        title: '请勾选互联网问诊知情同意书',
        icon: 'none',
        duration: 3000
      })
      return
    }

    // 校验通过，检查是否已经同意协议
    if (this.data.isAgreement) {
      // 已经同意协议，直接开始呼叫医生
      this.startVideoCall()
    } else {
      // 还未同意协议，显示知情同意书弹框
      this.showAgreementPopup()
    }
  },

  /**
   * 处理待接诊状态下的呼叫医生逻辑
   * 直接调用 api.startVideoCall 接口，成功后跳转到呼叫页面
   */
  async handleWaitingStatusCall() {
    console.log('待接诊状态，调用视频呼叫接口')

    try {
      // 显示loading状态
      wx.showLoading({
        title: '发起视频通话...',
        mask: true
      })

      // 构建接口参数
      const params = {
        patientId: this.data.patientId, // 患者ID
        videoConsultId: this.data.consultId // 视频咨询ID（使用consultId作为videoConsultId）
      }

      console.log('调用视频拨号接口，参数:', params)

      // 验证必要参数
      if (!params.patientId) {
        wx.hideLoading()
        wx.showToast({
          title: '用户信息异常，请重新登录',
          icon: 'none'
        })
        return
      }

      if (!params.videoConsultId) {
        wx.hideLoading()
        wx.showToast({
          title: '问诊信息异常，请重试',
          icon: 'none'
        })
        return
      }

      // 调用视频拨号接口
      const res = await util.request(api.startVideoCall, params, 'post', '2')

      wx.hideLoading()

      if (res.data.code === 0) {
        console.log('视频拨号接口调用成功:', res.data.data)

        // 从接口响应中提取医生信息
        const responseData = res.data.data
        console.log('startVideoCall API 完整响应数据结构:', JSON.stringify(responseData, null, 2))

        // 提取医生信息，支持多种可能的字段名
        const doctorInfo = this.extractDoctorInfo(responseData)
        console.log('提取的医生信息:', doctorInfo)

        // 将医生信息存储到全局数据中，供呼叫医生页面使用
        if (doctorInfo && Object.keys(doctorInfo).length > 0) {
          const app = getApp()
          if (app.globalData) {
            app.globalData.videoCallDoctorInfo = doctorInfo
            console.log('存储医生信息到全局数据:', doctorInfo)
          }
        }

        // 构建跳转URL，包含基础参数
        let url = `/pages_videoConsult/callDoctor/index?videoConsultId=${params.videoConsultId}&patientId=${params.patientId}`

        // 添加其他必要参数
        if (this.data.doctorId) {
          url += `&doctorId=${this.data.doctorId}`
        }
        if (this.data.consultId) {
          url += `&consultId=${this.data.consultId}`
        }
        if (this.data.currentPatient?.inquirerId) {
          url += `&inquirerId=${this.data.currentPatient.inquirerId}`
        }

        // 添加医生信息参数到URL（作为备用方案）
        if (doctorInfo) {
          url = this.addDoctorInfoToUrl(url, doctorInfo)
        }

        console.log('跳转到呼叫医生页面:', url)

        wx.navigateTo({
          url: url,
          success: () => {
            console.log('成功跳转到呼叫医生页面')
          },
          fail: (err) => {
            console.error('跳转到呼叫医生页面失败:', err)
            wx.showToast({
              title: '页面跳转失败，请重试',
              icon: 'none'
            })
          }
        })
      } else {
        console.error('视频拨号接口调用失败:', res.data.msg)
        wx.showToast({
          title: res.data.msg || '发起视频通话失败',
          icon: 'none',
          duration: 3000
        })
      }

    } catch (err) {
      console.error('视频拨号接口调用异常:', err)
      wx.hideLoading()
      wx.showToast({
        title: '网络异常，请稍后重试',
        icon: 'none',
        duration: 3000
      })
    }
  },

  /**
   * 从API响应中提取医生信息
   * @param {Object} responseData API响应数据
   * @returns {Object} 提取的医生信息对象
   */
  extractDoctorInfo(responseData) {
    try {
      // 如果响应数据为空，返回空对象
      if (!responseData || typeof responseData !== 'object') {
        console.log('响应数据为空或格式不正确')
        return {}
      }

      // 定义可能的医生信息字段映射
      const fieldMappings = {
        // 医生头像字段
        avatar: ['doctorHeadUrl', 'doctorPhoto', 'headUrl', 'avatar', 'photo'],
        // 医生姓名字段
        name: ['doctorName', 'name', 'realName'],
        // 医生职称字段
        title: ['doctorTitle', 'title', 'jobTitle', 'position'],
        // 医生专长字段
        specialty: ['doctorExpertise', 'expertise', 'specialty', 'specialization'],
        // 好评率字段
        goodRate: ['goodRate', 'rating', 'score'],
        // 服务次数字段
        serviceCount: ['serviceCount', 'consultCount', 'count']
      }

      const doctorInfo = {}

      // 遍历字段映射，提取医生信息
      Object.keys(fieldMappings).forEach(targetField => {
        const possibleFields = fieldMappings[targetField]
        for (const field of possibleFields) {
          if (responseData[field] !== undefined && responseData[field] !== null && responseData[field] !== '') {
            doctorInfo[targetField] = responseData[field]
            break // 找到第一个有效值就停止
          }
        }
      })

      console.log('提取医生信息结果:', doctorInfo)
      return doctorInfo

    } catch (err) {
      console.error('提取医生信息失败:', err)
      return {}
    }
  },

  /**
   * 将医生信息添加到URL参数中
   * @param {string} baseUrl 基础URL
   * @param {Object} doctorInfo 医生信息对象
   * @returns {string} 包含医生信息的完整URL
   */
  addDoctorInfoToUrl(baseUrl, doctorInfo) {
    try {
      let url = baseUrl

      // 添加医生信息到URL参数（进行URL编码）
      if (doctorInfo.name) {
        url += `&doctorName=${encodeURIComponent(doctorInfo.name)}`
      }
      if (doctorInfo.title) {
        url += `&doctorTitle=${encodeURIComponent(doctorInfo.title)}`
      }
      if (doctorInfo.specialty) {
        url += `&doctorSpecialty=${encodeURIComponent(doctorInfo.specialty)}`
      }
      if (doctorInfo.avatar) {
        url += `&doctorAvatar=${encodeURIComponent(doctorInfo.avatar)}`
      }
      if (doctorInfo.goodRate) {
        url += `&doctorGoodRate=${encodeURIComponent(doctorInfo.goodRate)}`
      }
      if (doctorInfo.serviceCount) {
        url += `&doctorServiceCount=${encodeURIComponent(doctorInfo.serviceCount)}`
      }

      return url

    } catch (err) {
      console.error('添加医生信息到URL失败:', err)
      return baseUrl
    }
  },

  /**
   * 发起视频问诊校验
   */
  async validatePatientInfo() {
    try {
      const currentPatient = this.data.currentPatient
      if (!currentPatient) {
        return {
          success: false,
          message: '请先选择就诊人'
        }
      }

      // 构建校验参数，包含视频问诊相关参数
      const params = {
        inquirerId: currentPatient.inquirerId,
        patientId: this.data.patientId
      }

      // 添加子订单编码和套餐编码
      if (this.data.subOrderCode) {
        params.subOrderCode = this.data.subOrderCode
      }
      if (this.data.packageCode) {
        params.packageCode = this.data.packageCode
      }

      console.log('校验就诊人信息参数:', params)

      // 调用后端接口校验就诊人信息
      const res = await util.request(api.validatePatientInfo, params, 'post', '2')

      if (res.data.code === 0) {
        return {
          success: true,
          message: '校验通过'
        }
      } else {
        // 根据不同的错误码进行不同的处理
        console.log('校验失败，错误码:', res.data.code, '错误信息:', res.data.msg)

        // 特殊错误码 730012：当前就诊人存在正在进行中的视频咨询
        if (res.data.code === 730012) {
          // 显示弹框提示，而不是普通的 toast
          wx.showModal({
            title: '无法发起新的问诊',
            content: '当前就诊人存在正在进行中的视频咨询，不能发起新的问诊',
            showCancel: true,
            cancelText: '取消',
            confirmText: '去查看',
            confirmColor: '#367DFF',
            success: (res) => {
              if (res.confirm) {
                // 用户点击"去查看"按钮，跳转到视频咨询列表页面
                console.log('用户选择去查看正在进行中的视频咨询')

                // 设置全局标识，用于目标页面自动切换到视频咨询 tab
                const app = getApp()
                if (app.globalData) {
                  app.globalData.switchToVideoConsultTab = true
                }

                // 使用 switchTab 跳转到 tabBar 页面
                wx.switchTab({
                  url: '/pages/consult/index/index',
                  success: () => {
                    console.log('成功跳转到咨询页面，将自动切换到视频咨询 tab')
                  },
                  fail: (err) => {
                    console.error('跳转到咨询页面失败:', err)
                  }
                })
              } else if (res.cancel) {
                // 用户点击"取消"按钮
                console.log('用户取消操作')
              }
            }
          })

          return {
            success: false,
            message: '当前就诊人存在正在进行中的视频咨询',
            showModal: true // 标识已显示弹框，调用方不需要再显示 toast
          }
        }

        // 其他错误码的处理
        const errorMessage = res.data.msg || '就诊人信息校验失败'

        return {
          success: false,
          message: errorMessage,
          showModal: false // 标识需要显示 toast
        }
      }

    } catch (err) {
      console.error('校验就诊人信息失败', err)
      return {
        success: false,
        message: '网络异常，请稍后重试'
      }
    }
  },

  /**
   * 显示知情同意书弹框
   */
  showAgreementPopup() {
    if (this.ageePopup) {
      this.ageePopup.setData({
        showPopup: true
      })
      this.setData({
        showPopup: true
      })
    }
  },

  /**
   * 用户同意协议
   */
  onAgreement() {
    this.setData({
      checked: true,
      isAgreement: true,
      showPopup: false
    })

    // 同意协议后，开始呼叫医生
    this.startVideoCall()
  },

  /**
   * 关闭协议弹框
   */
  onclosePopup() {
    const { isAgreement } = this.data
    this.setData({
      showPopup: false,
      checked: isAgreement ? true : false
    })
  },

  /**
   * 开始视频呼叫 - 发起视频咨询
   *
   * 流程说明：
   * 调用 videoPayInfo API 发起视频咨询
   *   - 参数：inquirerId, price(0), conditionDesc('无'),offlineDiagnosis('无'), subOrderCode, packageCode
   *   - 返回：医生信息和 videoConsultId
   *
   * 成功后直接跳转到呼叫医生页面，并传递医生信息
   */
  async startVideoCall() {
    try {
      wx.showLoading({
        title: '发起视频咨询...',
        mask: true
      })

      // 调用 videoPayInfo API 发起视频咨询
      const videoPayResult = await this.initiateVideoConsultation()
      wx.hideLoading()

      if (videoPayResult.success) {
        // 成功发起视频咨询，直接跳转到呼叫医生页面
        this.navigateToCallDoctor(videoPayResult.data)
      } else {
        wx.showToast({
          title: videoPayResult.message,
          icon: 'none',
          duration: 3000
        })
      }

    } catch (err) {
      console.error('视频呼叫失败', err)
      wx.hideLoading()
      wx.showToast({
        title: '网络异常，请稍后重试',
        icon: 'none'
      })
    }
  },

  /**
   * Step 1: 发起视频咨询 - 调用 videoPayInfo API
   */
  async initiateVideoConsultation() {
    try {
      const currentPatient = this.data.currentPatient
      if (!currentPatient) {
        return {
          success: false,
          message: '请先选择就诊人'
        }
      }

      // 构建 videoPayInfo API 参数
      const params = {
        inquirerId: currentPatient.inquirerId,
        price: 0, // 设置为 0
        conditionDesc: '无', // 设置为 "无"
        offlineDiagnosis: '无', // 线下诊断
        subOrderCode: this.data.subOrderCode,
        packageCode: this.data.packageCode
      }

      // 添加 assistantWelcomeId 参数（如果有的话）
      if (this.data.assistantWelcomeId) {
        params.assistantWelcomeId = this.data.assistantWelcomeId
        console.log('添加 assistantWelcomeId 参数:', this.data.assistantWelcomeId)
      }

      console.log('Step 1: 调用 videoPayInfo API，参数:', params)

      const res = await util.request(api.videoPayInfo, params, 'post', 1)

      if (res.data.code === 0) {
        console.log('Step 1: videoPayInfo API 调用成功，返回数据:', res.data.data)

        // 从响应中提取医生信息和视频咨询ID
        const responseData = res.data.data
        console.log('videoPayInfo API 完整响应数据结构:', JSON.stringify(responseData, null, 2))

        const doctorInfo = {
          avatar: responseData.doctorHeadUrl || responseData.doctorPhoto || responseData.headUrl || '', // 医生头像
          name: responseData.doctorName || responseData.name || '医生', // 医生姓名
          title: responseData.doctorTitle || responseData.title || '', // 医生职称
          specialty: responseData.doctorExpertise || responseData.expertise || responseData.specialty || '', // 医生专长
          // 其他可能的医生信息字段
          goodRate: responseData.goodRate || '',
          serviceCount: responseData.serviceCount || 0
        }

        console.log('提取的医生信息:', doctorInfo)

        // 验证关键字段
        const videoConsultId = responseData.videoConsultId || responseData.consultId || responseData.id
        if (!videoConsultId) {
          console.error('videoPayInfo API 响应中缺少 videoConsultId 字段')
          return {
            success: false,
            message: '服务器响应异常，请稍后重试'
          }
        }

        return {
          success: true,
          data: {
            videoConsultId: videoConsultId, // 视频咨询ID
            doctorInfo: doctorInfo, // 医生信息
            // 保留原始响应数据
            originalData: responseData
          }
        }
      } else {
        console.error('Step 1: videoPayInfo API 调用失败', res.data.msg)
        return {
          success: false,
          message: res.data.msg || '发起视频咨询失败'
        }
      }

    } catch (err) {
      console.error('Step 1: videoPayInfo API 调用异常', err)
      return {
        success: false,
        message: '网络异常，请稍后重试'
      }
    }
  },

  /**
   * 跳转到呼叫医生页面，传递医生信息
   */
  navigateToCallDoctor(videoPayData) {
    // 构建跳转参数
    let url = `/pages_videoConsult/callDoctor/index?doctorId=${this.data.doctorId}&consultId=${this.data.consultId}&inquirerId=${this.data.currentPatient?.inquirerId}&videoConsultId=${videoPayData.videoConsultId}`

    // 添加子订单编码和套餐编码参数
    if (this.data.subOrderCode) {
      url += `&subOrderCode=${this.data.subOrderCode}`
    }
    if (this.data.packageCode) {
      url += `&packageCode=${this.data.packageCode}`
    }

    // 将医生信息存储到全局数据中，供呼叫医生页面使用
    const app = getApp()
    if (app.globalData) {
      app.globalData.videoCallDoctorInfo = videoPayData.doctorInfo
      console.log('存储医生信息到全局数据:', videoPayData.doctorInfo)
    }

    console.log('跳转到呼叫医生页面:', url)

    // 跳转到呼叫医生页面
    wx.navigateTo({
      url: url
    })
  },

  /**
   * 跳转页面
   */
  goPage(e) {
    const { url } = e.currentTarget.dataset
    if (url) {
      wx.navigateTo({
        url: url
      })
    }
  },

  /**
   * 跳转到服务记录页面
   */
  goServiceRecord() {
    wx.navigateTo({
      url: '/pages_videoConsult/serviceRecord/index'
    })
  },

  /**
   * 协议勾选处理
   */
  onChecked(e) {
    const checked = e.detail
    this.setData({
      checked: checked
    })

    // 如果是勾选状态且还未同意过协议，则显示协议弹框
    if (checked && !this.data.isAgreement) {
      this.showAgreementPopup()
    }
  }
})
