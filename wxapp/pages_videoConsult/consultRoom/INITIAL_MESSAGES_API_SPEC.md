# 视频问诊初始助理消息接口规范

## 接口概述

新增接口用于获取视频问诊的初始助理消息，替代前端硬编码的助理消息，使消息内容可以通过后端动态配置。

## 接口定义

### 基本信息
- **接口名称**: 获取视频问诊初始助理消息
- **接口地址**: `GET /user/ap/patient/consult/video/initial/messages`
- **接口版本**: v2
- **调用方式**: GET 请求

### 在 api.js 中的定义
```javascript
getVideoConsultInitialMessages: WxApiRoot + 'user/ap/patient/consult/video/initial/messages' //get 获取视频问诊初始助理消息
```

## 请求参数

### Query Parameters
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| patientId | string | 是 | 患者ID | "12345" |
| consultId | string | 是 | 问诊ID | "67890" |
| consultType | number | 是 | 问诊类型，固定为2（视频问诊） | 2 |

### 请求示例
```
GET /user/ap/patient/consult/video/initial/messages?patientId=12345&consultId=67890&consultType=2
```

## 响应格式

### 成功响应
```json
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "id": "msg_001",
      "content": "我是您的招商信诺助理-小诺\n健康问题请点击蓝色按钮【呼叫视频医生】",
      "type": 1,
      "fromId": "assistant",
      "fromName": "小诺助理",
      "sendTime": 1703123456789,
      "order": 1
    },
    {
      "id": "msg_002", 
      "content": "恭喜您！会员服务开通成功",
      "type": 1,
      "fromId": "assistant",
      "fromName": "小诺助理",
      "sendTime": 1703123457789,
      "order": 2
    }
  ]
}
```

### 错误响应
```json
{
  "code": -1,
  "msg": "参数错误",
  "data": null
}
```

## 响应数据字段说明

### 根级字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | number | 响应状态码，0表示成功，非0表示失败 |
| msg | string | 响应消息 |
| data | array | 消息数组，可能为空数组 |

### 消息对象字段 (data数组中的元素)
| 字段名 | 类型 | 必填 | 说明 | 备注 |
|--------|------|------|------|------|
| id | string | 是 | 消息唯一标识 | 用于消息去重和标识 |
| content | string | 是 | 消息内容 | 支持换行符\n |
| type | number | 否 | 消息类型 | 1=文本消息，2=图片消息，默认为1 |
| fromId | string | 否 | 发送者ID | 默认为"assistant" |
| fromName | string | 否 | 发送者名称 | 默认为"小诺助理" |
| sendTime | number | 否 | 发送时间戳(毫秒) | 如果不提供，前端会自动生成 |
| order | number | 否 | 消息顺序 | 用于排序，数字越小越靠前 |

## 前端处理逻辑

### 1. 接口调用
```javascript
async getInitialMessagesFromAPI() {
  try {
    const params = {
      patientId: this.data.patientId,
      consultId: this.data.consultId,
      consultType: 2
    }

    const res = await util.request(api.getVideoConsultInitialMessages, params, 'GET', '2')

    if (res.data.code === 0) {
      return res.data.data || []
    } else {
      console.error('初始消息接口返回错误:', res.data.msg)
      return []
    }
  } catch (err) {
    console.error('初始消息接口调用异常:', err)
    throw err
  }
}
```

### 2. 数据转换
```javascript
processAndAddInitialMessages(backendMessages) {
  const currentTime = new Date().getTime()
  const messageArr = []

  backendMessages.forEach((backendMsg, index) => {
    // 将后端消息格式转换为前端消息格式
    const message = {
      id: backendMsg.id || `init_${index + 1}_${currentTime}`,
      sendTime: backendMsg.sendTime || (currentTime + index * 1000),
      from: {
        id: backendMsg.fromId || 'assistant',
        name: backendMsg.fromName || '小诺助理'
      },
      to: { id: this.data.patientId },
      type: backendMsg.type || 1,
      content: backendMsg.content || '',
      consultType: 2,
      consultId: this.data.consultId,
      relation: 1 // 助理消息，设为医生发送
    }

    messageArr.push({
      messages: [message],
      timeGroup: message.sendTime,
      sendTime: message.sendTime,
      timeText: util.calcTimeHeader(message.sendTime)
    })
  })

  this.setData({
    messageArr: messageArr,
    hasInitialMessages: true
  })
}
```

### 3. 降级处理
当接口调用失败时，前端会自动使用默认的硬编码消息：
```javascript
addDefaultInitialMessages() {
  // 使用原有的硬编码消息作为降级方案
  const messages = [
    {
      content: '我是您的招商信诺助理-小诺\n健康问题请点击蓝色按钮【呼叫\n视频医生】'
    },
    {
      content: '恭喜您！会员服务开通成功'
    }
  ]
  // ... 处理逻辑
}
```

## 业务场景

### 1. 正常流程
1. 用户进入视频咨询室页面
2. 前端调用初始消息接口
3. 后端返回配置的助理消息
4. 前端显示助理消息

### 2. 降级流程
1. 用户进入视频咨询室页面
2. 前端调用初始消息接口失败
3. 前端使用默认硬编码消息
4. 前端显示默认助理消息

### 3. 空消息处理
1. 后端返回空数组
2. 前端使用默认硬编码消息
3. 确保用户始终能看到助理消息

## 错误码定义

| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 0 | 成功 | 正常处理返回的消息 |
| -1 | 参数错误 | 使用默认消息 |
| -2 | 用户不存在 | 使用默认消息 |
| -3 | 问诊不存在 | 使用默认消息 |
| -4 | 系统异常 | 使用默认消息 |

## 配置建议

### 1. 消息内容配置
- 支持多条消息配置
- 支持换行符和特殊字符
- 消息内容应该简洁明了
- 可以根据不同场景配置不同消息

### 2. 消息顺序
- 使用 order 字段控制消息显示顺序
- 建议按10、20、30的间隔设置，便于后续插入新消息

### 3. 时间间隔
- 如果后端不提供 sendTime，前端会自动生成
- 建议消息间隔1-2秒，模拟真实对话

## 测试用例

### 1. 正常情况测试
```javascript
// 请求参数
{
  patientId: "12345",
  consultId: "67890", 
  consultType: 2
}

// 期望响应
{
  code: 0,
  data: [
    {
      id: "msg_001",
      content: "欢迎使用视频问诊服务",
      type: 1,
      fromName: "小诺助理"
    }
  ]
}
```

### 2. 空数据测试
```javascript
// 期望响应
{
  code: 0,
  data: []
}

// 前端行为：使用默认消息
```

### 3. 错误情况测试
```javascript
// 期望响应
{
  code: -1,
  msg: "参数错误",
  data: null
}

// 前端行为：使用默认消息
```

## 性能考虑

1. **缓存策略**: 建议后端对相同参数的请求进行短时间缓存
2. **响应时间**: 建议接口响应时间控制在500ms以内
3. **数据大小**: 建议单条消息内容不超过500字符
4. **并发处理**: 支持多用户同时请求

## 安全考虑

1. **参数验证**: 严格验证 patientId 和 consultId 的有效性
2. **权限检查**: 确保用户只能获取自己的问诊消息
3. **数据脱敏**: 消息内容不应包含敏感信息
4. **防刷机制**: 对同一用户的请求频率进行限制
