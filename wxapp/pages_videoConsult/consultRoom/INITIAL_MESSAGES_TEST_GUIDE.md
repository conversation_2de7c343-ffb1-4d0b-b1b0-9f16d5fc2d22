# 初始助理消息接口测试指南

## 测试目标

验证视频问诊初始助理消息从后端接口获取的功能是否正常工作，包括正常情况、异常情况和降级处理。

## 测试前准备

### 1. 接口准备
确保后端已实现以下接口：
```
GET /user/ap/patient/consult/video/initial/messages
```

### 2. 测试数据准备
- 有效的 patientId
- 有效的 consultId  
- 测试用的助理消息内容

### 3. 调试环境
- 开启浏览器控制台
- 监控网络请求
- 关注相关日志输出

## 核心测试用例

### 测试用例1：正常获取助理消息

**目标**：验证能正常从后端获取并显示助理消息

**前置条件**：
- 后端接口正常
- 有配置的助理消息

**测试步骤**：
1. 进入视频咨询室页面
2. 观察页面加载过程
3. 检查是否调用了初始消息接口
4. 验证助理消息是否正确显示

**预期结果**：
```javascript
// 控制台日志应显示：
"开始获取视频问诊初始助理消息"
"调用初始消息接口，参数: {patientId: '...', consultId: '...', consultType: 2}"
"初始消息接口调用成功: [...]"
"从后端获取到初始消息: [...]"
"后端初始消息处理完成，共 X 条消息"
```

**验证点**：
- ✅ 调用了 `getVideoConsultInitialMessages` 接口
- ✅ 接口参数正确
- ✅ 助理消息正确显示在聊天界面
- ✅ 消息格式正确（时间、发送者等）

### 测试用例2：后端返回空数据

**目标**：验证后端返回空数组时的降级处理

**模拟方法**：
让后端返回：
```json
{
  "code": 0,
  "msg": "success", 
  "data": []
}
```

**预期结果**：
```javascript
// 控制台日志应显示：
"后端未返回初始消息，使用默认消息"
"使用默认初始消息"
"默认初始消息添加完成"
```

**验证点**：
- ✅ 显示默认的硬编码助理消息
- ✅ 消息内容为原有的助理消息
- ✅ 用户体验无异常

### 测试用例3：接口调用失败

**目标**：验证接口调用失败时的降级处理

**模拟方法**：
```javascript
// 在控制台临时修改接口地址
const originalRequest = util.request
util.request = function(url, params, method, version) {
  if (url.includes('initial/messages')) {
    return Promise.reject(new Error('网络异常'))
  }
  return originalRequest.apply(this, arguments)
}
```

**预期结果**：
```javascript
// 控制台日志应显示：
"获取初始助理消息失败: Error: 网络异常"
"使用默认初始消息"
"默认初始消息添加完成"
```

**验证点**：
- ✅ 显示默认的硬编码助理消息
- ✅ 不影响页面正常使用
- ✅ 错误日志记录完整

### 测试用例4：接口返回错误

**目标**：验证接口返回错误码时的处理

**模拟方法**：
让后端返回：
```json
{
  "code": -1,
  "msg": "参数错误",
  "data": null
}
```

**预期结果**：
```javascript
// 控制台日志应显示：
"初始消息接口返回错误: 参数错误"
"后端未返回初始消息，使用默认消息"
"使用默认初始消息"
```

**验证点**：
- ✅ 正确处理错误响应
- ✅ 使用默认消息作为降级
- ✅ 错误信息记录清晰

### 测试用例5：消息格式转换

**目标**：验证后端消息格式正确转换为前端格式

**测试数据**：
```json
{
  "code": 0,
  "data": [
    {
      "id": "custom_msg_001",
      "content": "自定义助理消息内容\n支持换行",
      "type": 1,
      "fromId": "custom_assistant",
      "fromName": "自定义助理",
      "sendTime": 1703123456789,
      "order": 1
    }
  ]
}
```

**验证点**：
- ✅ 消息ID正确设置
- ✅ 消息内容正确显示（包括换行）
- ✅ 发送者信息正确
- ✅ 时间戳正确处理
- ✅ 消息类型正确

## 调试技巧

### 1. 监控接口调用
在浏览器开发者工具的Network标签页中：
- 筛选包含 `initial/messages` 的请求
- 检查请求参数是否正确
- 检查响应数据格式

### 2. 检查页面状态
```javascript
// 在控制台检查页面状态
const pages = getCurrentPages()
const currentPage = pages[pages.length - 1]
console.log('页面状态:', {
  hasInitialMessages: currentPage.data.hasInitialMessages,
  messageArr: currentPage.data.messageArr,
  patientId: currentPage.data.patientId,
  consultId: currentPage.data.consultId
})
```

### 3. 手动触发方法
```javascript
// 手动触发初始消息获取
const pages = getCurrentPages()
const currentPage = pages[pages.length - 1]
currentPage.setData({hasInitialMessages: false})
currentPage.addInitialMessages()
```

### 4. 模拟不同响应
```javascript
// 模拟成功响应
const mockSuccessResponse = {
  data: {
    code: 0,
    data: [
      {
        id: "test_msg_001",
        content: "测试助理消息",
        type: 1,
        fromName: "测试助理"
      }
    ]
  }
}

// 模拟失败响应
const mockErrorResponse = {
  data: {
    code: -1,
    msg: "测试错误",
    data: null
  }
}
```

## 性能测试

### 1. 响应时间测试
- 记录接口调用到消息显示的时间
- 目标：< 2秒

### 2. 并发测试
- 多个用户同时进入页面
- 验证接口是否能正常处理并发请求

### 3. 缓存测试
- 短时间内多次进入页面
- 验证是否有适当的缓存机制

## 边界条件测试

### 1. 大量消息测试
测试后端返回大量消息时的处理：
```json
{
  "code": 0,
  "data": [
    // 10条以上的消息
  ]
}
```

### 2. 长消息内容测试
测试消息内容很长时的显示：
```json
{
  "content": "很长很长的消息内容..." // 500+字符
}
```

### 3. 特殊字符测试
测试包含特殊字符的消息：
```json
{
  "content": "包含特殊字符：@#$%^&*()，中文，emoji😊"
}
```

## 回归测试清单

完成接口集成后，需要验证以下功能仍然正常：

- [ ] 视频咨询室页面正常加载
- [ ] 其他消息功能正常
- [ ] 历史消息加载正常
- [ ] 实时消息接收正常
- [ ] 页面性能无明显下降

## 常见问题排查

### 问题1：接口调用失败
**可能原因**：
- 接口地址错误
- 参数格式错误
- 权限验证失败

**排查方法**：
1. 检查 api.js 中的接口定义
2. 检查请求参数格式
3. 检查用户登录状态

### 问题2：消息不显示
**可能原因**：
- 数据格式转换错误
- 消息渲染逻辑问题
- 状态管理错误

**排查方法**：
1. 检查 `processAndAddInitialMessages` 方法
2. 检查 `messageArr` 数据结构
3. 检查 `hasInitialMessages` 状态

### 问题3：降级机制不生效
**可能原因**：
- 异常捕获不完整
- 默认消息逻辑错误

**排查方法**：
1. 检查 try-catch 覆盖范围
2. 检查 `addDefaultInitialMessages` 方法
3. 验证错误处理流程

## 测试报告模板

```
测试时间：[日期时间]
测试环境：[浏览器版本/设备信息]
测试人员：[姓名]

接口测试结果：
✅ 正常获取助理消息 - 通过
✅ 后端返回空数据 - 通过
✅ 接口调用失败 - 通过
✅ 接口返回错误 - 通过
✅ 消息格式转换 - 通过

性能测试结果：
- 接口响应时间：[X]ms
- 消息显示时间：[X]ms
- 内存使用：正常

发现问题：
[如有问题，详细描述]

建议：
[优化建议或注意事项]
```
